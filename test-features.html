<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能測試 - Read Aloud Pal</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
        }
        
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #4a90e2;
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: text;
        }
        
        .test-text:hover {
            background: #e9ecef;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .debug-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        h1 {
            color: #4a90e2;
            text-align: center;
        }
        
        h2 {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-working { background: #28a745; }
        .status-broken { background: #dc3545; }
        .status-unknown { background: #ffc107; }
    </style>
</head>
<body>
    <h1>🧪 Read Aloud Pal 功能測試</h1>

    <div class="instructions">
        <h3>📋 測試步驟</h3>
        <ol>
            <li>確保已安裝並啟用 Read Aloud Pal 擴展</li>
            <li>在擴展設置中配置 Google Gemini API Key</li>
            <li>打開瀏覽器開發者工具（F12）查看控制台日誌</li>
            <li>按照下方測試項目逐一測試</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎵 測試 1: 語音歷史紀錄</h2>
        <div class="debug-info">
            <div>狀態: <span class="status-indicator status-unknown"></span>待測試</div>
            <div>預期: 點擊擴展圖標，應該看到歷史紀錄區域</div>
        </div>
        <div class="test-text">
            這是測試語音歷史紀錄的文字。選擇這段文字並使用TTS功能，然後檢查擴展popup中是否出現歷史紀錄。
        </div>
        <p><strong>測試要點:</strong></p>
        <ul>
            <li>使用TTS後，popup中應該出現歷史紀錄</li>
            <li>點擊播放按鈕應該能重播語音</li>
            <li>點擊下載按鈕應該能下載WAV檔案</li>
            <li>清除按鈕應該能清空所有歷史</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>⏳ 測試 2: 等待處理畫面</h2>
        <div class="debug-info">
            <div>狀態: <span class="status-indicator status-unknown"></span>待測試</div>
            <div>預期: 選擇文字並點擊TTS時，應該出現載入覆蓋層</div>
        </div>
        <div class="test-text">
            這是測試等待處理畫面的文字。選擇這段文字並使用TTS功能，應該會看到一個帶有進度條的載入畫面。
        </div>
        <p><strong>測試要點:</strong></p>
        <ul>
            <li>點擊TTS後應該立即出現載入覆蓋層</li>
            <li>載入畫面應該顯示4個處理步驟</li>
            <li>進度條應該隨著步驟更新</li>
            <li>音頻開始播放時載入畫面應該消失</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>✨ 測試 3: 文字高亮功能</h2>
        <div class="debug-info">
            <div>狀態: <span class="status-indicator status-unknown"></span>待測試</div>
            <div>預期: 選擇文字並使用TTS時，文字應該被高亮顯示</div>
        </div>
        <div class="test-text">
            這是測試文字高亮功能的段落。當你選擇這段文字並使用TTS功能時，這段文字應該會被綠色背景高亮顯示，並在播放完成後自動清除高亮。
        </div>
        <p><strong>測試要點:</strong></p>
        <ul>
            <li>選擇文字並使用TTS時，文字應該立即被高亮</li>
            <li>高亮應該是綠色漸變背景</li>
            <li>播放完成後3秒，高亮應該自動消失</li>
            <li>可以同時高亮多個文字段落</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 測試 4: 綜合測試</h2>
        <div class="debug-info">
            <div>狀態: <span class="status-indicator status-unknown"></span>待測試</div>
            <div>預期: 所有功能應該協同工作</div>
        </div>
        <div class="test-text">
            這是綜合測試的文字段落。請選擇這段文字，然後觀察：1) 是否出現載入畫面，2) 文字是否被高亮，3) 播放完成後是否在歷史紀錄中出現，4) 是否可以從歷史紀錄重播。
        </div>
        <p><strong>測試要點:</strong></p>
        <ul>
            <li>所有三個新功能應該同時正常工作</li>
            <li>不應該有JavaScript錯誤</li>
            <li>用戶體驗應該流暢</li>
        </ul>
    </div>

    <div class="debug-info">
        <h3>🐛 調試信息</h3>
        <div>請在瀏覽器控制台中查看以下日誌:</div>
        <ul>
            <li>"🔄 Showing processing overlay..." - 載入畫面顯示</li>
            <li>"✨ Highlighting selected text..." - 開始高亮文字</li>
            <li>"✨ Text highlighted successfully" - 文字高亮成功</li>
            <li>"💾 Saved to history:" - 保存到歷史紀錄</li>
            <li>"🧹 Cleared all text highlights" - 清除高亮</li>
        </ul>
    </div>

    <script>
        // Add some debugging helpers
        console.log("🧪 Test page loaded - Ready for feature testing");
        
        // Monitor selection changes
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection.toString().trim()) {
                console.log(`📝 Text selected: "${selection.toString().substring(0, 50)}..."`);
            }
        });
        
        // Monitor for highlights
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.classList?.contains('read-aloud-pal-highlighted')) {
                            console.log("✅ Highlight element detected!");
                        }
                        if (node.classList?.contains('read-aloud-pal-loading-overlay')) {
                            console.log("✅ Loading overlay detected!");
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log("🔍 Monitoring for highlights and loading overlays...");
    </script>
</body>
</html>
