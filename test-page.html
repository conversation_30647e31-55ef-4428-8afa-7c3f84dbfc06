<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Read Aloud Pal 測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .chinese-text {
            background-color: #f8f8ff;
        }
        .english-text {
            background-color: #f0fff0;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-text {
            font-size: 16px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔊 Read Aloud Pal 測試頁面</h1>
    
    <div class="instructions">
        <h3>測試說明：</h3>
        <ol>
            <li>選擇下方任意文字</li>
            <li>點擊出現的播放按鈕 ▶️</li>
            <li>或者右鍵選擇「使用朗讀小夥伴唸出」</li>
            <li>檢查是否正確發音</li>
        </ol>
    </div>

    <div class="test-section chinese-text">
        <h2>中文測試文字</h2>
        <div class="test-text">
            <p>你好，歡迎使用朗讀小夥伴！這是一個專為兒童和語言學習者設計的 Chrome 擴充功能。</p>
            <p>這個工具可以幫助您練習發音，提高閱讀理解能力。請選擇這段文字來測試語音功能。</p>
            <p>數字測試：一二三四五六七八九十，1234567890</p>
            <p>標點符號測試：你好！這是問號嗎？是的，這是驚嘆號！</p>
        </div>
    </div>

    <div class="test-section english-text">
        <h2>English Test Text</h2>
        <div class="test-text">
            <p>Hello! Welcome to Read Aloud Pal! This is a Chrome extension designed for children and language learners.</p>
            <p>This tool can help you practice pronunciation and improve reading comprehension. Please select this text to test the speech functionality.</p>
            <p>Number test: one two three four five, 12345</p>
            <p>Punctuation test: Hello! Is this a question? Yes, this is an exclamation!</p>
        </div>
    </div>

    <div class="test-section">
        <h2>混合語言測試 (Mixed Language Test)</h2>
        <div class="test-text">
            <p>這是中英文混合的測試文字：Hello world 你好世界！</p>
            <p>Testing numbers: 123 和中文數字：一二三</p>
            <p>This is a mixed language test: 中文 and English together!</p>
        </div>
    </div>

    <div class="test-section">
        <h2>長文本測試 (Long Text Test)</h2>
        <div class="test-text">
            <p>
                這是一段較長的測試文字，用來測試擴充功能在處理較多內容時的表現。
                文字轉語音技術已經被廣泛應用在各種場景中，包括教育、無障礙輔助、
                語言學習等領域。現代的 TTS 系統能夠產生非常自然的語音，
                幫助使用者更好地理解和學習內容。這項技術對於視覺障礙者、
                閱讀困難者，以及語言學習者來說特別有用。
            </p>
            <p>
                This is a longer test text to check how the extension performs with more content.
                Text-to-speech technology has been widely used in various scenarios, including education,
                accessibility assistance, and language learning. Modern TTS systems can produce very natural speech,
                helping users better understand and learn content. This technology is particularly useful
                for visually impaired people, those with reading difficulties, and language learners.
            </p>
        </div>
    </div>

    <div class="test-section">
        <h2>特殊字符測試 (Special Characters Test)</h2>
        <div class="test-text">
            <p>符號測試：@#$%^&*()_+-=[]{}|;':\",./<>?</p>
            <p>表情符號：😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌</p>
            <p>Email: <EMAIL></p>
            <p>URL: https://www.example.com</p>
        </div>
    </div>

    <script>
        // Add some JavaScript to track selection events for debugging
        document.addEventListener('mouseup', function() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            if (selectedText) {
                console.log('Text selected:', selectedText);
            }
        });

        // Log when the page loads
        console.log('Read Aloud Pal test page loaded');
    </script>
</body>
</html> 