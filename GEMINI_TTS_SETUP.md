# Read Aloud Pal - Gemini TTS 設置指南

## 🎯 概述

Read Aloud Pal 現已升級支援 Google Gemini API 的高品質文字轉語音功能，提供比 Chrome 內建 TTS 更自然、更清晰的語音體驗。

## 🔧 設置步驟

### 1. 獲取 Google Gemini API Key

1. 訪問 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登入您的 Google 帳戶
3. 點擊「Create API Key」
4. 複製生成的 API Key（格式通常為 `AIza...`）

### 2. 配置擴展設置

1. 點擊瀏覽器工具欄中的 Read Aloud Pal 圖標
2. 在彈出的設置面板中找到「Google Gemini API Key」欄位
3. 貼上您的 API Key
4. 點擊眼睛圖標可以切換顯示/隱藏 API Key
5. 選擇您喜歡的語音風格（可選）
6. 設置會自動保存

### 3. 測試功能

1. 打開任意網頁或使用提供的測試頁面 `test-gemini-tts.html`
2. 選擇一段文字
3. 點擊浮動按鈕或右鍵選擇「朗讀選中文字」
4. 享受高品質的 Gemini TTS 語音

## 🎵 支援的語音風格

擴展支援 15 種不同的語音風格，每種都有獨特的特色：

- **Kore** - 堅定 (預設)
- **Puck** - 樂觀
- **Charon** - 資訊性
- **Leda** - 年輕
- **Fenrir** - 興奮
- **Aoede** - 輕快
- **Zephyr** - 明亮
- **Orus** - 堅定
- **Callirrhoe** - 隨和
- **Autonoe** - 明亮
- **Enceladus** - 氣息感
- **Iapetus** - 清晰
- **Umbriel** - 隨和
- **Algieba** - 順滑
- **Despina** - 順滑

## 🌍 支援的語言

- 🇹🇼 中文（繁體）- Traditional Chinese
- 🇨🇳 中文（簡體）- Simplified Chinese
- 🇺🇸 英語（美國）- English (US)
- 🇬🇧 英語（英國）- English (UK)
- 🇯🇵 日語 - Japanese
- 🇰🇷 韓語 - Korean

## ⚙️ 功能特色

### 🎯 智慧語音選擇
- 根據選擇的語言自動推薦最適合的語音
- 用戶可手動選擇偏好的語音風格

### 🔄 重複播放
- 支援 1-10 次重複播放
- 每次重複之間有短暫停頓

### 🎮 便捷控制
- 浮動按鈕快速控制
- 右鍵選單支援
- 播放/停止狀態即時反饋

### 🛡️ 錯誤處理
- API Key 格式驗證
- 網路錯誤自動重試
- 詳細錯誤訊息提示

### 🆕 語音歷史紀錄
- 自動保存最近 50 個語音檔案
- 一鍵重播歷史語音
- 下載語音檔案（WAV 格式）
- 顯示語言、語音風格和時間戳
- 一鍵清除所有歷史紀錄

### 🆕 處理進度顯示
- 4 步驟處理進度指示
- 美觀的載入動畫
- 即時狀態更新
- 可點擊背景取消

### 🆕 文字高亮標示
- 選中文字即時高亮顯示
- 綠色漸變背景效果
- 播放完成後自動清除
- 支援多段文字同時高亮

## 🔍 故障排除

### API Key 相關問題

**問題：顯示「API key is not configured」**
- 解決：確保在設置中正確輸入了 Gemini API Key

**問題：顯示「Invalid API key format」**
- 解決：檢查 API Key 格式，應該以 `AIza` 開頭且長度約 39 字符

**問題：顯示「Gemini API error: 403」**
- 解決：檢查 API Key 是否有效，是否已啟用 Gemini API

### 音頻播放問題

**問題：沒有聲音**
- 檢查瀏覽器音量設置
- 確保網頁允許音頻播放
- 嘗試重新整理頁面

**問題：音頻播放中斷**
- 檢查網路連接
- 嘗試選擇較短的文字段落
- 重新啟動擴展

### 連接問題

**問題：顯示「Could not establish connection」**
- 這通常表示 content script 未載入
- 解決方法：
  1. 重新整理頁面
  2. 重新啟動擴展
  3. 檢查是否在特殊頁面（如 chrome:// 頁面）
  4. 擴展會自動嘗試重新注入 content script

**問題：在某些頁面無法使用**
- Chrome 內部頁面（chrome://）不支援擴展
- 擴展頁面（chrome-extension://）不支援
- 某些受保護的頁面可能無法使用
- 建議在一般網頁上使用

### 性能優化

**問題：響應速度慢**
- 避免選擇過長的文字（建議 < 5000 字符）
- 檢查網路連接速度
- 考慮選擇不同的語音風格

## 📊 使用限制

- 單次文字長度限制：5000 字符
- API 調用頻率限制：依據 Google Gemini API 政策
- 支援的音頻格式：PCM 16-bit 24kHz

## 🔒 隱私與安全

- API Key 僅存儲在本地瀏覽器中
- 文字內容僅發送至 Google Gemini API 進行處理
- 不會收集或存儲用戶的個人資料

## 📞 技術支援

如果遇到問題，請：

1. 檢查瀏覽器控制台是否有錯誤訊息
2. 確認 API Key 設置正確
3. 嘗試使用測試頁面驗證功能
4. 重新安裝擴展（如必要）

## 🎉 享受體驗

現在您可以享受 Google Gemini 提供的高品質語音合成服務了！選擇任意文字，讓 AI 為您朗讀，體驗前所未有的自然語音效果。
