<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator for Read Aloud Pal</title>
</head>
<body>
    <h1>Icon Generator</h1>
    <p>This page generates basic icons for the Read Aloud Pal extension.</p>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas32" width="32" height="32" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="downloadIcons()">Download Icons</button>
    
    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            ctx.fillStyle = '#4a90e2';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // Play button triangle
            ctx.fillStyle = 'white';
            ctx.beginPath();
            const centerX = size/2;
            const centerY = size/2;
            const triangleSize = size * 0.3;
            
            ctx.moveTo(centerX - triangleSize/2, centerY - triangleSize/2);
            ctx.lineTo(centerX - triangleSize/2, centerY + triangleSize/2);
            ctx.lineTo(centerX + triangleSize/2, centerY);
            ctx.closePath();
            ctx.fill();
            
            return canvas;
        }
        
        function downloadIcons() {
            [16, 32, 48, 128].forEach(size => {
                const canvas = createIcon(size);
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        // Create icons on page load
        window.onload = function() {
            [16, 32, 48, 128].forEach(size => {
                createIcon(size);
            });
        };
    </script>
</body>
</html>
