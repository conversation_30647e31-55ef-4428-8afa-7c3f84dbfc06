// Debug script for Gemini TTS functionality
// Run this in the browser console to test and debug

console.log("=== Gemini TTS Debug Script ===");

// Test 1: Check if extension is loaded
console.log("1. Checking extension status...");
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log("✅ Chrome extension API available");
  
  // Check if our extension is responding
  chrome.runtime.sendMessage({action: "getStatus"}, (response) => {
    if (chrome.runtime.lastError) {
      console.log("❌ Extension not responding:", chrome.runtime.lastError.message);
    } else {
      console.log("✅ Extension responding:", response);
    }
  });
} else {
  console.log("❌ Chrome extension API not available");
}

// Test 2: Check storage settings
console.log("2. Checking stored settings...");
if (typeof chrome !== 'undefined' && chrome.storage) {
  chrome.storage.sync.get(['geminiApiKey', 'language', 'voiceName', 'repeatCount'], (settings) => {
    console.log("Current settings:", settings);
    
    if (settings.geminiApiKey) {
      console.log(`✅ API Key configured (${settings.geminiApiKey.length} chars)`);
      if (settings.geminiApiKey.startsWith('AIza')) {
        console.log("✅ API Key format looks correct");
      } else {
        console.log("⚠️ API Key format might be incorrect");
      }
    } else {
      console.log("❌ No API Key configured");
    }
    
    console.log(`Language: ${settings.language || 'zh-TW (default)'}`);
    console.log(`Voice: ${settings.voiceName || 'Auto-selected'}`);
    console.log(`Repeat Count: ${settings.repeatCount || '1 (default)'}`);
  });
}

// Test 3: Test text selection and TTS
console.log("3. Testing text selection...");
function testTTS(text = "Hello, this is a test of Gemini TTS functionality.") {
  console.log(`Testing TTS with text: "${text}"`);
  
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.sendMessage({
      action: "speak",
      text: text
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.log("❌ TTS test failed:", chrome.runtime.lastError.message);
      } else {
        console.log("✅ TTS test initiated:", response);
      }
    });
  } else {
    console.log("❌ Cannot test TTS - extension API not available");
  }
}

// Test 4: Audio context test
console.log("4. Testing Web Audio API...");
try {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  console.log("✅ Web Audio API available");
  console.log(`Sample rate: ${audioContext.sampleRate}Hz`);
  console.log(`Audio context state: ${audioContext.state}`);
  
  if (audioContext.state === 'suspended') {
    console.log("⚠️ Audio context is suspended - user interaction may be required");
  }
} catch (error) {
  console.log("❌ Web Audio API not available:", error.message);
}

// Test 5: Base64 decode test
console.log("5. Testing base64 decoding...");
function testBase64Decode() {
  try {
    const testData = "SGVsbG8gV29ybGQ="; // "Hello World" in base64
    const decoded = atob(testData);
    console.log("✅ Base64 decoding works:", decoded);
  } catch (error) {
    console.log("❌ Base64 decoding failed:", error.message);
  }
}
testBase64Decode();

// Utility functions for manual testing
window.debugGeminiTTS = {
  testTTS: testTTS,
  
  testWithChinese: () => {
    testTTS("這是中文語音合成測試。");
  },
  
  testWithEnglish: () => {
    testTTS("This is an English text-to-speech test.");
  },
  
  testWithJapanese: () => {
    testTTS("これは日本語の音声合成テストです。");
  },
  
  stopTTS: () => {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({action: "stop"}, (response) => {
        console.log("Stop TTS response:", response);
      });
    }
  },
  
  checkSettings: () => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.sync.get(null, (settings) => {
        console.log("All settings:", settings);
      });
    }
  },
  
  clearSettings: () => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.sync.clear(() => {
        console.log("Settings cleared");
      });
    }
  }
};

console.log("=== Debug Functions Available ===");
console.log("debugGeminiTTS.testTTS('your text') - Test TTS with custom text");
console.log("debugGeminiTTS.testWithChinese() - Test with Chinese text");
console.log("debugGeminiTTS.testWithEnglish() - Test with English text");
console.log("debugGeminiTTS.testWithJapanese() - Test with Japanese text");
console.log("debugGeminiTTS.stopTTS() - Stop current TTS");
console.log("debugGeminiTTS.checkSettings() - Check all settings");
console.log("debugGeminiTTS.clearSettings() - Clear all settings");

console.log("=== Ready for Testing ===");
console.log("1. Make sure your API key is configured in the extension popup");
console.log("2. Select some text on this page");
console.log("3. Click the floating button or use right-click menu");
console.log("4. Or use debugGeminiTTS.testTTS() to test programmatically");
