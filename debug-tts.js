// Debug script for TTS issues
// Run this in the browser console to test TTS functionality

console.log("=== Read Aloud Pal TTS Debug ===");

// Test 1: Check if TTS API is available
console.log("1. Checking TTS API availability...");
if (typeof chrome !== 'undefined' && chrome.tts) {
  console.log("✅ Chrome TTS API is available");
} else {
  console.log("❌ Chrome TTS API is NOT available");
}

// Test 2: Check available voices
console.log("2. Checking available voices...");
chrome.tts.getVoices((voices) => {
  console.log(`Found ${voices.length} TTS voices:`);
  voices.forEach((voice, index) => {
    console.log(`  ${index + 1}. ${voice.voiceName || 'Default'} (${voice.lang || 'Unknown'}) - ${voice.remote ? 'Remote' : 'Local'}`);
  });
  
  if (voices.length === 0) {
    console.log("❌ No TTS voices found. This might be the issue!");
    console.log("💡 Try:");
    console.log("   - Check system TTS settings");
    console.log("   - Install additional voices in system settings");
    console.log("   - Try on a different website");
  }
});

// Test 3: Simple TTS test
console.log("3. Testing basic TTS functionality...");
function testTTS(text, language) {
  console.log(`Testing TTS with text: "${text}" and language: ${language}`);
  
  chrome.tts.speak(text, {
    lang: language,
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    onEvent: (event) => {
      console.log(`TTS Event: ${event.type}`, event);
      if (event.type === 'start') {
        console.log("✅ TTS started successfully!");
      } else if (event.type === 'end') {
        console.log("✅ TTS completed successfully!");
      } else if (event.type === 'error') {
        console.log("❌ TTS error:", event.errorMessage);
      }
    }
  });
}

// Test with Chinese
setTimeout(() => {
  console.log("Testing Chinese TTS...");
  testTTS("你好，這是測試", "zh-CN");
}, 1000);

// Test with English
setTimeout(() => {
  console.log("Testing English TTS...");
  testTTS("Hello, this is a test", "en-US");
}, 3000);

// Test 4: Check extension permissions
console.log("4. Checking extension permissions...");
if (chrome.runtime && chrome.runtime.getManifest) {
  const manifest = chrome.runtime.getManifest();
  const hasTTSPermission = manifest.permissions && manifest.permissions.includes('tts');
  console.log(`TTS permission in manifest: ${hasTTSPermission ? '✅ Yes' : '❌ No'}`);
} else {
  console.log("Cannot check manifest (not in extension context)");
}

// Test 5: Check if we're in the right context
console.log("5. Checking execution context...");
console.log(`Current URL: ${window.location.href}`);
console.log(`User agent: ${navigator.userAgent}`);

// Instructions for user
console.log("\n=== Instructions ===");
console.log("1. Open Chrome DevTools (F12)");
console.log("2. Go to Console tab");
console.log("3. Look for any error messages");
console.log("4. Check if TTS events are logged");
console.log("5. If no voices found, check system TTS settings");
console.log("\n=== Common Issues ===");
console.log("- No system TTS voices installed");
console.log("- TTS permission missing from manifest");
console.log("- Website blocking TTS (try on a simple page)");
console.log("- Browser TTS disabled in settings");
console.log("- Extension not properly loaded");
