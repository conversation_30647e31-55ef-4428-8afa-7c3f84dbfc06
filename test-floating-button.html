<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Read Aloud Pal - 浮動按鈕測試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4a90e2;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #e9ecef;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Read Aloud Pal 浮動按鈕測試</h1>
        
        <div class="instructions">
            <h3>📋 測試步驟：</h3>
            <ol>
                <li>確保 Read Aloud Pal 擴充功能已安裝並啟用</li>
                <li>按 F12 打開開發者工具，切換到 Console 標籤</li>
                <li>反白下方的測試文字</li>
                <li>觀察是否出現浮動播放按鈕</li>
                <li>點擊浮動按鈕並查看控制台訊息</li>
                <li>也可以嘗試右鍵選單的「使用朗讀小夥伴唸出」</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🇹🇼 繁體中文測試</h2>
            <p>請反白這段繁體中文文字來測試語音功能。這是一個專門為測試 Read Aloud Pal 擴充功能而設計的測試頁面。如果一切正常，您應該會看到一個小的播放按鈕出現在選取文字的附近。</p>
            
            <p>台灣是一個美麗的島嶼，擁有豐富的文化和歷史。從北部的台北101到南部的墾丁國家公園，每個地方都有其獨特的魅力。</p>
        </div>

        <div class="test-section">
            <h2>🇨🇳 簡體中文測試</h2>
            <p>请选中这段简体中文文字来测试语音功能。这是一个专门为测试 Read Aloud Pal 扩展功能而设计的测试页面。如果一切正常，您应该会看到一个小的播放按钮出现在选取文字的附近。</p>
            
            <p>中国是一个历史悠久的国家，拥有五千年的文明史。从万里长城到紫禁城，每一个古迹都诉说着深厚的历史文化。</p>
        </div>

        <div class="test-section">
            <h2>🇺🇸 English Test</h2>
            <p>Please select this English text to test the speech functionality. This is a test page specifically designed for testing the Read Aloud Pal extension. If everything works correctly, you should see a small play button appear near the selected text.</p>
            
            <p>The United States is a diverse country with a rich history and culture. From the Statue of Liberty in New York to the Golden Gate Bridge in San Francisco, each landmark tells a unique story.</p>
        </div>

        <div class="test-section">
            <h2>🇯🇵 日本語テスト</h2>
            <p>この日本語のテキストを選択して音声機能をテストしてください。これは Read Aloud Pal 拡張機能をテストするために特別に設計されたテストページです。すべてが正常に動作する場合、選択したテキストの近くに小さな再生ボタンが表示されるはずです。</p>
        </div>

        <div class="debug-info">
            <h3>🔍 調試資訊：</h3>
            <p><strong>預期的控制台訊息：</strong></p>
            <ul>
                <li>🔘 Floating button clicked! - 當點擊浮動按鈕時</li>
                <li>▶️ Starting speech with text: - 開始語音播放時</li>
                <li>🎯 TTS Request - Language: zh-TW - TTS請求資訊</li>
                <li>✅ Using voice: [語音名稱] - 使用的語音</li>
            </ul>
            
            <p><strong>如果浮動按鈕沒有出現：</strong></p>
            <ul>
                <li>檢查擴充功能是否正確載入</li>
                <li>確認浮動按鈕設定已啟用</li>
                <li>重新載入此頁面</li>
                <li>嘗試在其他網站測試</li>
            </ul>
            
            <p><strong>如果點擊按鈕沒有反應：</strong></p>
            <ul>
                <li>查看控制台是否有錯誤訊息</li>
                <li>確認系統已安裝TTS語音</li>
                <li>嘗試使用右鍵選單</li>
                <li>檢查瀏覽器音量設定</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 手動測試腳本</h2>
            <p>在控制台執行以下腳本來測試TTS功能：</p>
            <pre style="background: #f1f3f4; padding: 10px; border-radius: 5px; overflow-x: auto;">
// 測試繁體中文
chrome.tts.speak("你好，這是繁體中文測試", {
  lang: "zh-TW",
  onEvent: (event) => console.log("TTS Event:", event.type, event)
});

// 檢查可用語音
chrome.tts.getVoices((voices) => {
  console.log("可用語音:", voices.map(v => `${v.voiceName} (${v.lang})`));
});
            </pre>
        </div>
    </div>

    <script>
        // 添加一些調試資訊
        console.log("🧪 Test page loaded");
        console.log("Extension context available:", !!window.chrome?.runtime);
        console.log("TTS API available:", !!window.chrome?.tts);
        
        // 監聽文字選擇事件
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            if (selectedText) {
                console.log("📝 Text selected:", selectedText.substring(0, 50) + "...");
            }
        });
    </script>
</body>
</html>
