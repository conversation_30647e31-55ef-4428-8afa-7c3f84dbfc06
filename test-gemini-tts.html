<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Gemini TTS - Read Aloud Pal</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .test-section {
        background: white;
        margin: 20px 0;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .test-text {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
        border-left: 4px solid #4a90e2;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .test-text:hover {
        background: #e9ecef;
      }

      .test-text.selected {
        background: #d4edda;
        border-left-color: #28a745;
      }

      .instructions {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
      }

      .status {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
      }

      h1 {
        color: #4a90e2;
        margin-bottom: 10px;
      }

      h2 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
      }

      .language-label {
        display: inline-block;
        background: #4a90e2;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-bottom: 8px;
      }

      .emoji {
        font-size: 1.2em;
        margin-right: 8px;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🎤 Gemini TTS 測試頁面</h1>
      <p>測試 Read Aloud Pal 的新 Google Gemini TTS 功能</p>
    </div>

    <div class="instructions">
      <h3>📋 測試說明</h3>
      <ol>
        <li>確保已安裝並啟用 Read Aloud Pal 擴展</li>
        <li>在擴展設置中配置您的 Google Gemini API Key</li>
        <li>選擇下方任意文字段落</li>
        <li>點擊浮動按鈕或右鍵選擇「朗讀選中文字」</li>
        <li>測試不同語言和語音設置</li>
      </ol>
    </div>

    <div class="status">
      <h3>🔧 功能檢查清單</h3>
      <ul>
        <li>✅ API Key 設置界面</li>
        <li>✅ 語音選擇功能</li>
        <li>✅ 多語言支持</li>
        <li>✅ 重複播放功能</li>
        <li>✅ 浮動按鈕控制</li>
        <li>✅ 錯誤處理機制</li>
        <li>🆕 語音歷史紀錄（重播/下載）</li>
        <li>🆕 處理進度顯示</li>
        <li>🆕 文字高亮標示</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>🇹🇼 繁體中文測試</h2>
      <div class="language-label">zh-TW</div>
      <div class="test-text">
        <span class="emoji">📚</span>
        這是一段繁體中文的測試文字。Google Gemini
        的語音合成技術能夠產生自然流暢的中文語音，讓使用者享受高品質的朗讀體驗。
      </div>
      <div class="test-text">
        <span class="emoji">🌟</span>
        人工智慧技術的發展日新月異，語音合成已經達到了前所未有的自然度和表現力。透過深度學習模型，我們可以創造出幾乎與真人無異的語音效果。
      </div>
    </div>

    <div class="test-section">
      <h2>🇨🇳 简体中文测试</h2>
      <div class="language-label">zh-CN</div>
      <div class="test-text">
        <span class="emoji">🚀</span>
        这是一段简体中文的测试文字。先进的语音合成技术让计算机能够说出自然流畅的中文，为用户提供优质的听觉体验。
      </div>
      <div class="test-text">
        <span class="emoji">💡</span>
        科技改变生活，人工智能正在重塑我们与数字世界的交互方式。语音技术的进步让信息获取变得更加便捷和自然。
      </div>
    </div>

    <div class="test-section">
      <h2>🇺🇸 English Test</h2>
      <div class="language-label">en-US</div>
      <div class="test-text">
        <span class="emoji">🎯</span>
        This is an English test paragraph. Google Gemini's advanced
        text-to-speech technology delivers natural and expressive voice
        synthesis that enhances the user experience.
      </div>
      <div class="test-text">
        <span class="emoji">🌍</span>
        Artificial intelligence continues to revolutionize how we interact with
        technology. Voice synthesis has reached remarkable levels of naturalness
        and emotional expression.
      </div>
    </div>

    <div class="test-section">
      <h2>🇯🇵 日本語テスト</h2>
      <div class="language-label">ja-JP</div>
      <div class="test-text">
        <span class="emoji">🌸</span>
        これは日本語のテスト文章です。最新の音声合成技術により、自然で聞きやすい日本語の音声を生成することができます。
      </div>
      <div class="test-text">
        <span class="emoji">🎌</span>
        人工知能の発展により、コンピューターと人間のコミュニケーションがより自然になっています。音声技術の進歩は素晴らしいものです。
      </div>
    </div>

    <div class="test-section">
      <h2>🇰🇷 한국어 테스트</h2>
      <div class="language-label">ko-KR</div>
      <div class="test-text">
        <span class="emoji">🏮</span>
        이것은 한국어 테스트 문장입니다. 고급 음성 합성 기술을 통해 자연스럽고
        명확한 한국어 음성을 생성할 수 있습니다.
      </div>
      <div class="test-text">
        <span class="emoji">🎭</span>
        인공지능 기술의 발전으로 컴퓨터와 인간의 상호작용이 더욱 자연스러워지고
        있습니다. 음성 기술의 진보는 놀라운 수준에 도달했습니다.
      </div>
    </div>

    <div class="test-section">
      <h2>🔧 技術測試</h2>
      <div class="language-label">Mixed</div>
      <div class="test-text">
        <span class="emoji">⚡</span>
        長文測試：Lorem ipsum dolor sit amet, consectetur adipiscing elit.
        這是一段包含多種語言的測試文字，用來測試語音合成系統的穩定性和準確性。人工智慧技術
        artificial intelligence 正在改變我們的生活方式。
      </div>
      <div class="test-text">
        <span class="emoji">🎵</span>
        特殊字符測試：Hello! 你好！こんにちは！안녕하세요！123456789 @#$%^&*()
        測試標點符號、數字和特殊符號的處理能力。
      </div>
    </div>

    <script>
      // Add selection highlighting
      document.addEventListener("mouseup", function () {
        // Remove previous highlights
        document.querySelectorAll(".test-text.selected").forEach((el) => {
          el.classList.remove("selected");
        });

        // Highlight selected text container
        const selection = window.getSelection();
        if (selection.toString().trim()) {
          let container = selection.anchorNode;
          while (container && !container.classList?.contains("test-text")) {
            container = container.parentNode;
          }
          if (container) {
            container.classList.add("selected");
          }
        }
      });

      // Log for debugging
      console.log("Gemini TTS Test Page Loaded");
      console.log("Ready for testing Read Aloud Pal with Gemini TTS");
      console.log("Open browser console and run debug commands if needed");
    </script>

    <!-- Load debug script -->
    <script src="debug-gemini-tts.js"></script>
  </body>
</html>
