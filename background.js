// Background script for Read Aloud Pal Chrome Extension
// Handles TTS functionality using Google Gemini API and context menu creation

// Gemini TTS Configuration
const GEMINI_API_ENDPOINT =
  "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent";

// Voice mapping for different languages
const GEMINI_VOICE_MAP = {
  "zh-TW": "Kore", // Firm voice for Traditional Chinese
  "zh-CN": "Kore", // Firm voice for Simplified Chinese
  "en-US": "Puck", // Upbeat voice for English
  "en-GB": "<PERSON>ron", // Informative voice for British English
  "ja-<PERSON>": "<PERSON><PERSON>", // Youthful voice for Japanese
  "ko-KR": "Fenrir", // Excitable voice for Korean
};

// Global state management for TTS queue
let currentSpeechState = {
  isPlaying: false,
  currentTabId: null,
  currentText: "",
  currentRepeatCount: 0,
  totalRepeats: 1,
  requestId: null, // Unique identifier for current request
  startTime: null, // When current speech started
  timeoutId: null, // Timeout for detecting TTS failures
};

// Queue management
let speechQueue = [];
let isProcessingQueue = false;

// Initialize extension when installed
chrome.runtime.onInstalled.addListener(async () => {
  console.log("Read Aloud Pal extension installed");

  try {
    // Check if settings already exist
    const existingSettings = await chrome.storage.sync.get([
      "language",
      "repeatCount",
      "showFloatingButton",
    ]);

    // Set default settings only if they don't exist
    const defaultSettings = {
      language: existingSettings.language || "zh-TW", // Default to Traditional Chinese
      repeatCount: existingSettings.repeatCount || 1,
      showFloatingButton: existingSettings.showFloatingButton !== false,
    };

    await chrome.storage.sync.set(defaultSettings);
    console.log("Settings initialized:", defaultSettings);
  } catch (error) {
    console.error("Error initializing settings:", error);
    // Fallback to basic settings
    chrome.storage.sync.set({
      language: "zh-CN",
      repeatCount: 1,
      showFloatingButton: true,
    });
  }

  // Create context menu item
  createContextMenu();

  console.log("Gemini TTS service initialized");

  // Update context menu title based on default language
  setTimeout(updateContextMenuTitle, 100);
});

// Listen for storage changes to update context menu and validate settings
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === "sync") {
    if (changes.language) {
      updateContextMenuTitle();
    }

    // Validate and fix any invalid settings
    validateAndFixSettings(changes);
  }
});

// Validate and fix invalid settings
async function validateAndFixSettings(changes) {
  try {
    const fixes = {};
    let needsFix = false;

    // Validate language
    const validLanguages = [
      "zh-TW",
      "zh-CN",
      "en-US",
      "en-GB",
      "ja-JP",
      "ko-KR",
    ];
    if (
      changes.language &&
      !validLanguages.includes(changes.language.newValue)
    ) {
      fixes.language = "zh-TW";
      needsFix = true;
      console.warn("Invalid language setting detected, fixing to default");
    }

    // Validate repeat count
    if (changes.repeatCount) {
      const count = changes.repeatCount.newValue;
      if (typeof count !== "number" || count < 1 || count > 10) {
        fixes.repeatCount = 1;
        needsFix = true;
        console.warn("Invalid repeat count detected, fixing to default");
      }
    }

    // Validate floating button setting
    if (
      changes.showFloatingButton &&
      typeof changes.showFloatingButton.newValue !== "boolean"
    ) {
      fixes.showFloatingButton = true;
      needsFix = true;
      console.warn(
        "Invalid floating button setting detected, fixing to default"
      );
    }

    // Apply fixes if needed
    if (needsFix) {
      await chrome.storage.sync.set(fixes);
      console.log("Settings fixed:", fixes);
    }
  } catch (error) {
    console.error("Error validating settings:", error);
  }
}

// Create context menu with proper error handling
function createContextMenu() {
  try {
    // Remove existing context menu items first
    chrome.contextMenus.removeAll(() => {
      // Create the main context menu item
      chrome.contextMenus.create(
        {
          id: "readAloudPal",
          title: "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)",
          contexts: ["selection"],
        },
        () => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error creating context menu:",
              chrome.runtime.lastError
            );
          } else {
            console.log("Context menu created successfully");
          }
        }
      );
    });
  } catch (error) {
    console.error("Failed to create context menu:", error);
  }
}

// Get the best voice for the given language using Gemini TTS
function getBestVoiceForLanguage(language) {
  return GEMINI_VOICE_MAP[language] || "Kore"; // Default to Kore
}

// Send audio data to content script with retry logic
async function sendAudioToContentScript(tabId, message, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(
        `Attempting to send audio to content script (attempt ${attempt}/${maxRetries})`
      );

      // First check if the tab exists and is accessible
      const tab = await chrome.tabs.get(tabId);
      if (!tab) {
        throw new Error("Tab not found");
      }

      // Check if it's a special page that doesn't support content scripts
      if (
        tab.url.startsWith("chrome://") ||
        tab.url.startsWith("chrome-extension://") ||
        tab.url.startsWith("moz-extension://") ||
        tab.url.startsWith("about:")
      ) {
        throw new Error("Cannot inject content script into special pages");
      }

      // Try to send the message
      await chrome.tabs.sendMessage(tabId, message);
      console.log("✅ Audio data sent to content script successfully");
      return;
    } catch (error) {
      console.warn(`Attempt ${attempt} failed:`, error.message);

      if (attempt === maxRetries) {
        // Last attempt failed, try to inject content script and retry once more
        try {
          console.log("Trying to inject content script...");
          await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ["content.js"],
          });

          // Wait a bit for content script to initialize
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Try sending message one more time
          await chrome.tabs.sendMessage(tabId, message);
          console.log("✅ Audio data sent after content script injection");
          return;
        } catch (injectionError) {
          console.error("Failed to inject content script:", injectionError);
          currentSpeechState.isPlaying = false;
          throw new Error(
            `Failed to communicate with content script: ${error.message}`
          );
        }
      }

      // Wait before retry
      await new Promise((resolve) => setTimeout(resolve, 200 * attempt));
    }
  }
}

// Generate speech using Gemini API
async function generateGeminiSpeech(text, language, voiceName, apiKey) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.warn("Gemini API request timed out after 30 seconds");
  }, 30000); // 30-second timeout

  try {
    if (!apiKey) {
      throw new Error(
        "Gemini API key is required. Please configure it in the extension settings."
      );
    }

    if (!text || text.trim().length === 0) {
      throw new Error("Text is required");
    }

    // Limit text length to avoid API limits
    if (text.length > 5000) {
      text = text.substring(0, 5000);
      console.warn("Text truncated to 5000 characters for TTS");
    }

    // Use the provided voiceName (already determined by caller)

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: text,
            },
          ],
        },
      ],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceName,
            },
          },
        },
      },
    };

    console.log(
      `🎯 Gemini TTS Request - Language: ${language}, Voice: ${voiceName}`
    );
    console.log(`Text: "${text.substring(0, 100)}..."`);

    const response = await fetch(`${GEMINI_API_ENDPOINT}?key=${apiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal, // Add AbortController signal
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        `Gemini API error: ${response.status} - ${
          errorData.error?.message || response.statusText
        }`
      );
    }

    const data = await response.json();

    if (
      !data.candidates ||
      !data.candidates[0] ||
      !data.candidates[0].content ||
      !data.candidates[0].content.parts ||
      !data.candidates[0].content.parts[0] ||
      !data.candidates[0].content.parts[0].inlineData
    ) {
      throw new Error("Invalid response format from Gemini API");
    }

    const audioData = data.candidates[0].content.parts[0].inlineData.data;

    if (!audioData) {
      throw new Error("No audio data received from Gemini API");
    }

    console.log("✅ Gemini TTS response received successfully");
    return audioData;
  } catch (error) {
    console.error("Gemini TTS Error:", error);
    if (error.name === 'AbortError') {
      throw new Error('Gemini API request timed out. Please try again.');
    }
    throw error;
  } finally {
    clearTimeout(timeoutId); // Clear the timeout
  }
}

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "readAloudPal") {
    handleContextMenuClick(info, tab);
  }
});

// Handle context menu click with validation
function handleContextMenuClick(info, tab) {
  try {
    // Validate selection text
    if (!info.selectionText || info.selectionText.trim().length === 0) {
      console.warn("No text selected for context menu TTS");
      return;
    }

    // Validate tab
    if (!tab || !tab.id) {
      console.error("Invalid tab for context menu TTS");
      return;
    }

    // Check text length limits
    const selectedText = info.selectionText.trim();
    if (selectedText.length > 5000) {
      console.warn("Selected text too long for TTS, truncating");
    }

    // Log context menu usage
    console.log(
      "Context menu TTS triggered for text:",
      selectedText.substring(0, 50) + (selectedText.length > 50 ? "..." : "")
    );

    // Handle the TTS request
    const requestId = generateRequestId();
    handleTTSRequest(selectedText, tab.id, requestId);
  } catch (error) {
    console.error("Error handling context menu click:", error);
  }
}

// Update context menu title based on current language
async function updateContextMenuTitle() {
  try {
    const settings = await chrome.storage.sync.get(["language"]);
    const language = settings.language || "zh-CN";

    let title;
    if (language.startsWith("zh")) {
      title = "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)";
    } else {
      title = "Read with Read Aloud Pal (使用朗讀小夥伴唸出)";
    }

    chrome.contextMenus.update("readAloudPal", { title: title }, () => {
      if (chrome.runtime.lastError) {
        console.error(
          "Error updating context menu title:",
          chrome.runtime.lastError
        );
      }
    });
  } catch (error) {
    console.error("Error updating context menu title:", error);
  }
}

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "speak") {
    const requestId = generateRequestId();
    handleTTSRequest(request.text, sender.tab.id, requestId);
    sendResponse({ success: true, requestId: requestId });
  } else if (request.action === "stop") {
    stopCurrentSpeech();
    sendResponse({ success: true });
  } else if (request.action === "getStatus") {
    sendResponse({
      isPlaying: currentSpeechState.isPlaying,
      currentTabId: currentSpeechState.currentTabId,
      requestId: currentSpeechState.requestId,
    });
  }
  return true; // Keep message channel open for async response
});

// Generate unique request ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Save TTS result to history
async function saveToHistory(text, language, voiceName, audioData, requestId) {
  try {
    const historyItem = {
      id: requestId,
      text: text.substring(0, 500), // Limit text length for storage
      language: language,
      voiceName: voiceName,
      audioData: audioData,
      timestamp: Date.now(),
    };

    // Get existing history
    const result = await chrome.storage.local.get(["ttsHistory"]);
    let history = result.ttsHistory || [];

    // Add new item to beginning
    history.unshift(historyItem);

    // Keep only last 50 items to prevent storage overflow
    if (history.length > 50) {
      history = history.slice(0, 50);
    }

    // Save back to storage
    await chrome.storage.local.set({ ttsHistory: history });

    console.log(`💾 Saved to history: "${text.substring(0, 50)}..."`);
  } catch (error) {
    console.error("Error saving to history:", error);
    // Don't throw error as this is not critical for TTS functionality
  }
}

// Stop current speech and reset state
function stopCurrentSpeech(reason = "manual") {
  console.log(`Stopping current speech: ${reason}`);

  // Note: Audio stopping will be handled by content script

  // Store previous state for notification
  const previousTabId = currentSpeechState.currentTabId;
  const previousRequestId = currentSpeechState.requestId;

  // Clear any pending timeout
  if (currentSpeechState.timeoutId) {
    clearTimeout(currentSpeechState.timeoutId);
  }

  // Reset state
  currentSpeechState = {
    isPlaying: false,
    currentTabId: null,
    currentText: "",
    currentRepeatCount: 0,
    totalRepeats: 1,
    requestId: null,
    startTime: null,
    timeoutId: null,
  };

  // Clear any pending queue processing
  isProcessingQueue = false;

  // Notify content script if there's an active tab
  if (previousTabId) {
    chrome.tabs
      .sendMessage(previousTabId, {
        action: "speechComplete",
        reason: reason,
        requestId: previousRequestId,
      })
      .catch(() => {
        // Ignore errors if tab is closed or content script not available
      });
  }

  // Notify all other tabs that speech has stopped
  notifyAllTabsOfSpeechStop(previousTabId);
}

// Notify all tabs (except the current one) that speech has stopped
function notifyAllTabsOfSpeechStop(excludeTabId) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id !== excludeTabId) {
        chrome.tabs
          .sendMessage(tab.id, {
            action: "speechStoppedGlobally",
          })
          .catch(() => {
            // Ignore errors for tabs without content script
          });
      }
    });
  });
}

// Main TTS handling function with queue management
async function handleTTSRequest(text, tabId, requestId) {
  try {
    // Validate input
    if (!text || text.trim().length === 0) {
      console.warn("Empty text provided for TTS");
      return;
    }

    if (text.length > 5000) {
      console.warn("Text too long for TTS, truncating to 5000 characters");
      text = text.substring(0, 5000);
    }

    // Check if there's already speech playing
    if (currentSpeechState.isPlaying) {
      console.log(
        `Interrupting current speech (tab ${currentSpeechState.currentTabId}) for new request (tab ${tabId})`
      );
      stopCurrentSpeech("interrupted");
    }

    // Get user settings including API key
    const settings = await chrome.storage.sync.get([
      "language",
      "voiceName",
      "repeatCount",
      "geminiApiKey",
    ]);
    const language = settings.language || "zh-TW";
    const voiceName = settings.voiceName || getBestVoiceForLanguage(language);
    const repeatCount = Math.max(1, Math.min(10, settings.repeatCount || 1));
    const apiKey = settings.geminiApiKey;

    if (!apiKey) {
      throw new Error(
        "Gemini API key is not configured. Please set your API key in the extension settings."
      );
    }

    console.log(
      `🎯 TTS Request - Language: ${language}, Repeat: ${repeatCount}`
    );

    // Update state with new request
    currentSpeechState = {
      isPlaying: true,
      currentTabId: tabId,
      currentText: text,
      currentRepeatCount: 0,
      totalRepeats: repeatCount,
      requestId: requestId,
      startTime: Date.now(),
    };

    console.log(
      `Starting TTS for tab ${tabId}, request ${requestId}: "${text.substring(
        0,
        50
      )}..."`
    );

    // Generate speech using Gemini TTS and send to content script
    await speakWithGeminiTTS(
      text,
      language,
      voiceName,
      apiKey,
      repeatCount,
      tabId,
      requestId
    );
  } catch (error) {
    console.error("Error handling TTS request:", error);

    // Reset state on error
    currentSpeechState.isPlaying = false;

    // Notify content script of error
    chrome.tabs
      .sendMessage(tabId, {
        action: "speechError",
        error: error.message,
        requestId: requestId,
      })
      .catch(() => {});
  }
}

// Function to handle speech with Gemini TTS and repeat functionality
async function speakWithGeminiTTS(
  text,
  language,
  voiceName,
  apiKey,
  repeatCount,
  tabId,
  requestId
) {
  try {
    console.log(`🎤 Starting Gemini TTS for: "${text.substring(0, 50)}..."`);

    // Update processing step - generating speech
    try {
      await chrome.tabs.sendMessage(tabId, {
        action: "updateProcessingStep",
        step: 1,
      });
    } catch (e) {
      // Ignore if content script not available
    }

    // Generate speech audio using Gemini API
    const audioData = await generateGeminiSpeech(
      text,
      language,
      voiceName,
      apiKey
    );

    // Send audio data to content script for playback with repeat functionality
    await sendAudioToContentScript(tabId, {
      action: "playGeminiAudio",
      audioData: audioData,
      repeatCount: repeatCount,
      requestId: requestId,
      text: text.substring(0, 100) + "...",
    });

    console.log("✅ Audio data sent to content script for playback");

    // Save to history
    await saveToHistory(text, language, voiceName, audioData, requestId);
  } catch (error) {
    console.error("Gemini TTS speak error:", error);
    currentSpeechState.isPlaying = false;

    // Try to notify content script of error, but don't fail if it's not available
    try {
      await chrome.tabs.sendMessage(tabId, {
        action: "speechError",
        error: error.message,
        requestId: requestId,
      });
    } catch (notificationError) {
      console.warn(
        "Could not notify content script of error:",
        notificationError.message
      );

      // If we can't reach content script, show a browser notification instead
      try {
        await chrome.notifications.create({
          type: "basic",
          iconUrl: "icons/icon48.png",
          title: "Read Aloud Pal",
          message: `TTS Error: ${error.message}`,
        });
      } catch (notifError) {
        console.warn("Could not show notification:", notifError.message);
      }
    }

    throw error;
  }
}
