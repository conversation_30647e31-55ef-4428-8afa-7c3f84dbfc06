# PRD: 修復多項功能故障

**日期:** 2025-07-03 23:30:19

## 1. 需求背景

使用者回報 Read Aloud Pal 擴充功能存在多項功能故障，影響核心使用體驗。本次任務旨在診斷並修復這些問題，提升產品穩定性。

## 2. 問題描述與修復紀錄

本次任務處理了四個主要問題：

### 問題一：等待處理畫面故障

-   **現象:** 使用者在觸發 TTS 功能後，表示處理中的載入畫面沒有正常顯示或無法消失，導致 UI 卡死。
-   **根本原因分析:**
    1.  `background.js` 中對 Gemini API 的請求沒有設置超時，當 API 反應緩慢或無回應時，前景 `content.js` 的載入畫面會一直等待。
    2.  `content.js` 中，處理來自背景的 `speechError` 訊息時，沒有觸發隱藏載入畫面的邏輯。
-   **修復方案:**
    1.  **增加 API 請求超時:** 在 `background.js` 的 `generateGeminiSpeech` 函式中，為 `fetch` 請求增加了 30 秒的超時機制 (`AbortController`)。如果請求超時，會主動拋出錯誤，並通知前景。
    2.  **完善錯誤處理:** 在 `content.js` 的 `speechError` 事件監聽器中，補上了 `hideProcessingOverlay()` 的呼叫，確保在收到任何錯誤時都能關閉載入畫面。

### 問題二：文字高亮功能故障

-   **現象:** 語音朗讀時，對應的網頁文字沒有被高亮，或者高亮效果在朗讀結束後沒有被清除，造成頁面顯示異常。
-   **根本原因分析:**
    -   高亮清除的邏輯不完整。只有在部分情境下 (`handleButtonClick` 手動停止、`handleAudioComplete` 正常播畢) 才會清除高亮。如果語音因為錯誤、被新請求中斷、或被其他分頁的朗讀停止，高亮效果會殘留在頁面上。
-   **修復方案:**
    -   **統一清除邏輯:** 在 `content.js` 中，將 `clearHighlights()` 函式添加到了所有語音結束的事件處理路徑中，包括 `speechComplete`、`speechError`、`speechStoppedGlobally` 和 `handleAudioError`，確保無論語音因何種原因停止，高亮都能被可靠地清除。

### 問題三：下載語音檔案功能故障

-   **現象:** 在彈出視窗的歷史紀錄中，點擊下載按鈕無法下載可播放的語音檔案，或者下載的檔案已損毀。
-   **根本原因分析:**
    -   `popup.js` 中的 `convertPCMToCompressedAudio` 函式，在嘗試使用 `MediaRecorder` API 壓縮音訊失敗時，其備援方案 (fallback) 存在嚴重錯誤。它直接將原始的 PCM 音訊數據儲存為 `.wav` 檔案，但沒有寫入必要的 WAV 檔案標頭 (header)，導致產生的檔案無法被任何播放器識別。
-   **修復方案:**
    -   **實作正確的 WAV 封裝:** 新增了一個 `createWavBlob` 輔助函式。該函式會根據 PCM 數據的參數（取樣率、聲道等）生成一個符合規範的 44 位元組 WAV 標頭，並將其與原始 PCM 數據合併成一個有效的 `Blob` 物件。現在，當 `MediaRecorder` 壓縮失敗時，使用者會下載到一個可以正常播放的 WAV 檔案。

### 問題四：Floating Button 按下去沒有反應

-   **現象:** 在網頁上選取文字後，浮動的播放按鈕出現，但點擊後沒有任何反應，既不播放語音，也沒有載入畫面。
-   **根本原因分析:**
    -   這是一個狀態管理問題。當使用者點擊按鈕，`content.js` 會將 `isLoading` 狀態設為 `true`，並向 `background.js` 發送 `speak` 訊息。如果 `background.js` 因故（例如服務被終止後重啟、內部錯誤）沒有回應，`content.js` 的 `isLoading` 狀態就永遠不會被重設為 `false`。這導致後續所有按鈕點擊都會因為 `if (this.isLoading)` 的檢查而提前返回，造成沒有反應的假象。
-   **修復方案:**
    -   **增加前景請求超時:** 在 `content.js` 的 `handleButtonClick` 函式中，增加了一個客戶端側的超時機制。在發送 `speak` 訊息後，會啟動一個 10 秒的計時器。如果 10 秒內沒有收到來自背景腳本的任何回音（如 `speechStart` 或其他訊息），則會判定背景腳本無回應，並自動重置 UI 狀態（隱藏載入畫面、清除高亮、將 `isLoading` 設回 `false`），同時提示使用者錯誤，避免按鈕被永久鎖定。 