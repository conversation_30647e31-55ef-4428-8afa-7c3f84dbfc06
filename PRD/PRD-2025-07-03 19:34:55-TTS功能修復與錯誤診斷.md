# PRD - TTS功能修復與錯誤診斷

**日期**: 2025-07-03 19:34:55  
**專案**: Read Aloud Pal (TTS 朗讀小夥伴)  
**類型**: Bug 修復和功能優化

## 問題描述

用戶回報了兩個主要問題：
1. 點擊浮動按鈕沒有發音
2. Chrome 控制台出現錯誤：`Unrecognized manifest key 'description_zh'`

## 問題分析

### 1. Manifest 錯誤
- **問題**: `description_zh` 不是 Chrome Extension Manifest V3 的標準鍵值
- **影響**: 雖然不會影響功能，但會產生控制台警告
- **解決方案**: 移除非標準的 `description_zh` 欄位

### 2. TTS 功能無聲音問題
- **可能原因**:
  - 系統沒有可用的 TTS 語音引擎
  - TTS 權限未正確初始化
  - 錯誤處理不夠詳細，無法識別具體問題
  - Chrome TTS API 調用失敗但未提供明確錯誤訊息

## 實施的解決方案

### 1. 修復 Manifest 錯誤

```json
// 移除了非標準的 description_zh 欄位
- "description_zh": "專為兒童及語言學習者設計的 Chrome 擴充功能，提供高品質的文字朗讀功能。",
```

### 2. 增強 TTS 錯誤處理和診斷

#### 2.1 改善語音初始化 (`background.js`)
```javascript
function initializeTTSVoices() {
  chrome.tts.getVoices((voices) => {
    console.log(`Available TTS voices: ${voices.length}`);
    
    if (voices.length === 0) {
      console.warn("No TTS voices available. TTS functionality may not work.");
    } else {
      // 詳細記錄可用語音
      voices.forEach((voice, index) => {
        console.log(`Voice ${index}: ${voice.voiceName || 'Default'} (${voice.lang || 'Unknown'}) - ${voice.remote ? 'Remote' : 'Local'}`);
      });
    }
    
    chrome.storage.local.set({ availableVoices: voices });
  });
}
```

#### 2.2 增強 TTS 請求處理
- 添加詳細的事件日誌記錄
- 在調用 TTS 前檢查語音可用性
- 改善錯誤訊息的用戶友好性
- 添加 try-catch 錯誤處理

```javascript
// 檢查 TTS 可用性
chrome.tts.getVoices((voices) => {
  if (voices.length === 0) {
    console.error("No TTS voices available on this system");
    // 通知用戶
    chrome.tabs.sendMessage(tabId, {
      action: "speechError",
      error: "No TTS voices available - please check system settings",
      requestId: requestId,
    });
    return;
  }

  // 嘗試 TTS 調用
  try {
    chrome.tts.speak(text, ttsOptions);
  } catch (error) {
    console.error("Error calling chrome.tts.speak:", error);
    // 錯誤處理...
  }
});
```

### 3. 創建測試頁面

創建了 `test-page.html` 來方便測試：
- 包含中英文測試文字
- 混合語言測試
- 長文本測試
- 特殊字符測試
- 詳細的使用說明

## 診斷建議

如果 TTS 功能仍然無法正常工作，用戶可以：

1. **檢查瀏覽器控制台**:
   - 開啟開發者工具 (F12)
   - 查看 Console 標籤中的錯誤訊息
   - 尋找以 "TTS" 開頭的日誌訊息

2. **檢查系統 TTS 設置**:
   - macOS: 系統偏好設定 → 輔助使用 → 語音
   - Windows: 設定 → 輕鬆存取 → 朗讀程式
   - 確保有安裝中文和英文語音包

3. **測試步驟**:
   - 開啟 `test-page.html`
   - 選擇簡短的中文或英文文字
   - 觀察控制台輸出
   - 檢查是否有語音播放

4. **權限檢查**:
   - 確認擴充功能已正確安裝並啟用
   - 檢查 Chrome 是否允許 TTS 權限

## 技術詳情

### 修改的文件
1. `manifest.json` - 移除非標準欄位
2. `background.js` - 增強錯誤處理和診斷
3. `test-page.html` - 新增測試頁面

### 測試環境
- Chrome 瀏覽器（最新版本）
- macOS 系統
- 擴充功能開發者模式

## 後續改進建議

1. **用戶界面反饋**: 當 TTS 不可用時，在頁面上顯示更明確的錯誤提示
2. **後備方案**: 考慮集成第三方 TTS 服務作為系統 TTS 的後備
3. **語音設置**: 添加語音選擇和語速調整功能
4. **自動診斷**: 開發自動診斷工具，幫助用戶識別和解決 TTS 問題

## 驗證清單

- [x] 修復 manifest.json 錯誤
- [x] 增強錯誤處理和日誌記錄
- [x] 創建測試頁面
- [x] 添加語音可用性檢查
- [x] 改善用戶錯誤訊息
- [ ] 用戶測試確認問題解決

## 結論

通過增強錯誤處理、改善診斷信息和修復 manifest 錯誤，應該能夠幫助識別和解決 TTS 功能問題。如果問題持續存在，詳細的日誌輸出將幫助進一步診斷根本原因。 