// Performance monitoring utility for Read Aloud Pal
// This file can be included for development testing

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      textSelections: 0,
      ttsRequests: 0,
      errors: 0,
      averageResponseTime: 0,
      memoryUsage: [],
    };
    this.startTime = Date.now();
    this.responseTimes = [];
  }

  // Track text selection events
  trackTextSelection() {
    this.metrics.textSelections++;
    console.log(`Text selections: ${this.metrics.textSelections}`);
  }

  // Track TTS requests with timing
  trackTTSRequest(startTime) {
    this.metrics.ttsRequests++;
    const responseTime = Date.now() - startTime;
    this.responseTimes.push(responseTime);
    
    // Calculate average response time
    this.metrics.averageResponseTime = 
      this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
    
    console.log(`TTS requests: ${this.metrics.ttsRequests}, Avg response: ${this.metrics.averageResponseTime.toFixed(2)}ms`);
  }

  // Track errors
  trackError(error) {
    this.metrics.errors++;
    console.error(`Extension error #${this.metrics.errors}:`, error);
  }

  // Monitor memory usage
  trackMemoryUsage() {
    if (performance.memory) {
      const usage = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };
      
      this.metrics.memoryUsage.push(usage);
      
      // Keep only last 100 measurements
      if (this.metrics.memoryUsage.length > 100) {
        this.metrics.memoryUsage.shift();
      }
      
      console.log(`Memory usage: ${(usage.used / 1024 / 1024).toFixed(2)} MB`);
    }
  }

  // Get performance report
  getReport() {
    const uptime = Date.now() - this.startTime;
    const report = {
      ...this.metrics,
      uptime: uptime,
      uptimeFormatted: this.formatTime(uptime),
      errorRate: this.metrics.errors / Math.max(this.metrics.ttsRequests, 1),
      selectionsPerMinute: (this.metrics.textSelections / uptime) * 60000,
      requestsPerMinute: (this.metrics.ttsRequests / uptime) * 60000,
    };
    
    console.table(report);
    return report;
  }

  // Format time in human readable format
  formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // Start automatic monitoring
  startMonitoring(interval = 30000) {
    setInterval(() => {
      this.trackMemoryUsage();
    }, interval);
    
    console.log(`Performance monitoring started (interval: ${interval}ms)`);
  }

  // Export metrics for analysis
  exportMetrics() {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      report: this.getReport()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `read-aloud-pal-metrics-${Date.now()}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('Metrics exported');
  }
}

// Usage example:
// const monitor = new PerformanceMonitor();
// monitor.startMonitoring();
// 
// // In your extension code:
// monitor.trackTextSelection();
// monitor.trackTTSRequest(startTime);
// monitor.trackError(error);
// 
// // Get report:
// monitor.getReport();
// monitor.exportMetrics();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceMonitor;
} else if (typeof window !== 'undefined') {
  window.PerformanceMonitor = PerformanceMonitor;
}
