## **產品需求文件 (PRD): TTS 朗讀小夥伴 (Read Aloud Pal)**
### **1. 專案總覽 (Project Overview)**

#### **1.1 產品名稱**
TTS 朗讀小夥伴 (Read Aloud Pal)

#### **1.2 產品簡介 (Elevator Pitch)**
這是一款專為兒童及語言學習者設計的 Chrome 擴充功能。使用者只需在任何網頁上圈選文字，點擊浮動按鈕，即可觸發高品質的真人語音朗讀。產品支援中英文切換、重複朗讀設定，旨在幫助孩子們透過「聽」來加強識字能力和句子發音，讓網頁瀏覽變成一個有趣的學習過程。

#### **1.3 問題陳述 (Problem Statement)**
1.  **兒童識字挑戰**：孩子們在瀏覽網頁（如：線上故事書、百科知識）時，常遇到不認識的字或詞，打斷了閱讀的流暢性與興趣。
2.  **發音學習不易**：家長不一定總在身邊，或不確定某些單字/句子的標準發音，特別是學習第二語言（如英文）時。
3.  **現有工具複雜**：市面上的 TTS 工具多為成人設計，功能過於複雜，缺乏針對兒童學習特性（如：重複記憶）的優化。

#### **1.4 解決方案 (Solution)**
開發一個極簡、直觀的 Chrome 擴充功能，提供以下核心價值：
*   **即時反饋**：圈選即朗讀，立即解決識字和發音問題。
*   **專注學習**：可設定重複朗讀，強化特定字詞或句子的聽覺記憶。
*   **簡單易用**：專為兒童和非技術背景的家長設計，介面清爽，操作直覺。

#### **1.5 專案目標 (Goals)**
*   **使用者目標**：提升兒童的閱讀興趣和獨立識字能力；輔助語言學習者的發音練習。
*   **業務目標 (V1.0)**：在 Chrome Web Store 上架，達到 1,000+ 週活躍用戶，並獲得 4.5 星以上的平均評分。

---

### **2. 目標用戶與使用情境 (Target Audience & Use Cases)**

#### **2.1 主要目標用戶**
*   **學齡前及國小低年級兒童 (3-8歲)**：正在學習認字和閱讀。
*   **家長**：希望為孩子提供一個安全、有效的數位學習輔助工具。

#### **2.2 次要目標用戶**
*   **語言學習者**：任何年齡的中文或英文學習者，希望透過聆聽來校正發音。
*   **有閱讀障礙的使用者**：需要語音輔助來理解網頁內容。

#### **2.3 使用情境 (Use Cases)**
*   **情境一：認讀單字**
    *   **用戶**：6歲的小明正在看一篇關於恐龍的線上文章。
    *   **操作**：他看到「暴龍」這個詞不認識，用滑鼠圈選「暴龍」。
    *   **結果**：文字旁邊出現一個小小的播放圖示，小明點擊後，聽到清晰的中文語音「暴龍」。他媽媽在設定中設了重複3次，所以他連續聽了三遍，加深了印象。

*   **情境二：學習句子發音**
    *   **用戶**：陳媽媽正在陪孩子讀英文線上繪本。
    *   **操作**：她想教孩子 "The quick brown fox jumps over the lazy dog." 這句話的語調，於是圈選了整個句子。
    *   **結果**：點擊播放圖示後，流暢的英文語音唸出整個句子。她們可以重複聆聽，直到孩子掌握了句子的節奏和發音。

*   **情境三：設定調整**
    *   **用戶**：家長 (或高年級的自學者)。
    *   **操作**：點擊瀏覽器右上角的「朗讀小夥伴」圖示。
    *   **結果**：彈出一個小視窗，可以輕鬆將預設語言從「中文」切換到「英文」，並將重複次數從 3 次調整到 5 次。

---

### **3. 功能需求 (Functional Requirements)**

#### **FR-1: 核心朗讀功能**
*   **FR-1.1 文字圈選觸發**：當用戶在網頁上使用滑鼠選取一段文字後，在選取文字的右側或右上方應出現一個小巧的、非侵入式的「播放」浮動圖示。
*   **FR-1.2 點擊播放**：點擊該浮動圖示後，系統應根據用戶設定的語言和重複次數，朗讀所選的文字。
*   **FR-1.3 右鍵選單觸發**：除了浮動圖示，用戶圈選文字後，點擊滑鼠右鍵，應在右鍵選單中出現一個「使用朗讀小夥伴唸出」的選項，點擊後效果同上。此為替代方案，避免浮動按鈕在某些網頁上失效或被遮擋。
*   **FR-1.4 播放中斷**：在朗讀期間，浮動圖示應變為「停止」圖示。用戶可點擊該圖示隨時中斷朗讀。再次點擊則重新開始。
*   **FR-1.5 佇列管理**：若用戶在前一段語音未播放完畢時，又點擊了新的朗讀請求，系統應立即停止當前的朗讀，並開始播放新的內容。

#### **FR-2: 設定面板 (Popup UI)**
*   **FR-2.1 面板入口**：點擊 Chrome 工具列上的「朗讀小夥伴」擴充功能圖示，會彈出一個設定面板。
*   **FR-2.2 語言設定**：
    *   提供一個下拉式選單，選項包含：
        *   **中文 (Mandarin)**
        *   **英文 (English)**
    *   預設值為「中文」。
    *   此設定決定了朗讀時使用的語音引擎。
*   **FR-2.3 重複次數設定**：
    *   提供一個數字輸入框或滑桿，讓用戶設定朗讀的重複次數。
    *   範圍為 1 到 10 次。
    *   預設值為 1 次。
*   **FR-2.4 開關浮動按鈕**：提供一個開關 (Toggle Switch)，讓用戶可以選擇是否要在圈選文字後顯示浮動播放圖示。預設為「開啟」。
*   **FR-2.5 設定儲存**：所有設定應自動儲存，並在瀏覽器重啟後依然有效。

#### **FR-3: 使用者介面 (UI/UX)**
*   **FR-3.1 擴充功能圖示**：一個簡潔、友好的圖示，例如一個帶有播放符號的對話泡泡。
*   **FR-3.2 浮動播放圖示**：尺寸小（約 24x24 px），半透明，滑鼠懸浮時變為不透明。設計需可愛且易於辨識。
*   **FR-3.3 設定面板介面**：佈局清晰，字體稍大，確保家長和孩子都能輕鬆操作。

---

### **4. 非功能性需求 (Non-Functional Requirements)**

*   **NFR-1: 效能 (Performance)**
    *   **回應速度**：從點擊播放到開始發聲的延遲應小於 500 毫秒。
    *   **資源佔用**：擴充功能在閒置狀態下不應佔用過多 CPU 或記憶體，避免拖慢用戶的瀏覽器。
*   **NFR-2: 相容性 (Compatibility)**
    *   支援最新版本的 Google Chrome 瀏覽器。
    *   應能在絕大多數靜態和動態網頁上正常運作。
*   **NFR-3: 安全性與隱私 (Security & Privacy)**
    *   **最小權限原則**：只請求必要的權限 (`activeTab`, `contextMenus`, `storage`, `tts`)。
    *   **無數據收集**：本擴充功能 V1.0 不會收集、儲存或傳輸任何用戶圈選的文字內容或個人資訊。

---

### **5. 技術規格與架構 (Technical Specifications & Architecture)**

*   **核心技術**：HTML, CSS, JavaScript (ES6+)。
*   **Chrome Extension APIs**:
    *   `manifest.json`: 定義擴充功能的基本資訊和權限。
    *   `Content Scripts`: 注入到網頁中，用於偵測文字選取事件和顯示浮動按鈕。
    *   `Background Scripts`: 處理核心邏輯，接收來自 Content Script 的請求，並調用 TTS API。
    *   `chrome.tts`: 使用瀏覽器內建的 TTS 引擎來發聲。這是 V1.0 的首選，因其免費、離線可用且效能良好。
    *   `chrome.contextMenus`: 創建右鍵選單項目。
    *   `chrome.storage.sync`: 儲存用戶設定（如語言、重複次數），並可在用戶登入同一個 Google 帳號的不同裝置間同步。
*   **架構流程**:
    1.  **用戶圈選文字** -> `Content Script` 偵測 `mouseup` 事件，並取得選取的文字。
    2.  `Content Script` 顯示浮動按鈕。
    3.  **用戶點擊按鈕** -> `Content Script` 將選取的文字和當前設定發送訊息給 `Background Script`。
    4.  `Background Script` 接收到訊息，從 `chrome.storage` 讀取語言和重複次數設定。
    5.  `Background Script` 呼叫 `chrome.tts.speak()`，傳入文字、語言、重複次數等參數。
    6.  瀏覽器 TTS 引擎執行朗讀。

---

### **6. 未來版本規劃 (Future Roadmap / V2.0)**

*   **V1.1 - 優化**
    *   **語音選擇**：允許用戶在同一語言下選擇不同的聲音（如男聲、女聲、童聲）。
    *   **語速與音調調整**：在設定面板增加滑桿來控制語速和音調。
*   **V2.0 - 功能擴展**
    *   **多語言支援**：增加日文、韓文、西班牙文等更多語言選項。
    *   **自動語言偵測**：嘗試自動偵測所選文字的語言，減少用戶手動切換的麻煩。
    *   **朗讀時文字高亮**：在朗讀長句子時，逐字或逐詞高亮正在發音的部分。
    *   **學習記錄**：(可選功能) 記錄用戶查詢過的單字，形成個人化單字本。

---
