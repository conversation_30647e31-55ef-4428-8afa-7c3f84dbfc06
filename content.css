/* Styles for Read Aloud Pal floating button */

.read-aloud-pal-button {
  width: 28px;
  height: 28px;
  background: rgba(74, 144, 226, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  font-weight: bold;
}

.read-aloud-pal-button:hover {
  transform: scale(1.15);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 1);
}

.read-aloud-pal-button:active {
  transform: scale(0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.read-aloud-pal-button:focus {
  outline: 3px solid rgba(74, 144, 226, 0.5);
  outline-offset: 2px;
}

/* Loading state */
.read-aloud-pal-button[data-loading="true"] {
  cursor: wait;
  animation: pulse 1.5s ease-in-out infinite;
}

/* Playing state */
.read-aloud-pal-button[data-playing="true"] {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  to {
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.4);
  }
}

/* Ensure the button doesn't interfere with page content */
.read-aloud-pal-button {
  pointer-events: auto;
  z-index: 2147483647; /* Maximum z-index value */
}

/* Processing overlay styles */
.read-aloud-pal-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2147483647;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.loading-content {
  background: white;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 320px;
  width: 90%;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 16px;
  line-height: 1.4;
}

.loading-progress {
  margin-top: 16px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2, #357abd);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Additional animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Text highlight styles */
.read-aloud-pal-highlighted {
  background: linear-gradient(120deg, #a8e6cf 0%, #88d8a3 100%);
  color: #2d5a3d;
  padding: 2px 4px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: highlightFadeIn 0.3s ease-out;
  position: relative;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Pulsing effect for highlighted text during speech */
.read-aloud-pal-highlighted.speaking {
  animation: highlightPulse 2s ease-in-out infinite;
}

.read-aloud-pal-highlighted::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(120deg, #4a90e2, #357abd);
  border-radius: 5px;
  z-index: -1;
  opacity: 0.1;
}

@keyframes highlightFadeIn {
  from {
    background: #ffeb3b;
    transform: scale(1.05);
  }
  to {
    background: linear-gradient(120deg, #a8e6cf 0%, #88d8a3 100%);
    transform: scale(1);
  }
}

@keyframes highlightPulse {
  0%,
  100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    opacity: 1;
  }
  50% {
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
    opacity: 0.9;
  }
}
