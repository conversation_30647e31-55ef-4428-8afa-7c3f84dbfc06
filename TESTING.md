# Read Aloud Pal - Testing Guide

## Functional Requirements Testing

### FR-1.1: Text Selection Detection and Floating <PERSON>ton Display
- [ ] Select text on various websites (news, blogs, Wikipedia)
- [ ] Verify floating button appears near selected text
- [ ] Test button positioning at viewport edges
- [ ] Verify button is 24x24px and semi-transparent
- [ ] Test on pages with complex layouts and z-index conflicts
- [ ] Verify button doesn't appear when toggle is disabled

### FR-1.2: Core TTS Playback Functionality
- [ ] Test TTS with Chinese text (default language)
- [ ] Test TTS with English text
- [ ] Verify repeat functionality (1-10 times)
- [ ] Test with very short text (1 character)
- [ ] Test with long text (5000+ characters)
- [ ] Verify voice selection works properly
- [ ] Test error handling for TTS failures

### FR-1.3: Right-Click Context Menu Integration
- [ ] Select text and right-click to see context menu
- [ ] Verify context menu title changes with language setting
- [ ] Test context menu on various websites
- [ ] Verify context menu works when floating button is disabled
- [ ] Test with different text selections (single word, paragraph)

### FR-1.4: Playback Control (Stop/Resume)
- [ ] Start TTS and verify button changes to stop icon
- [ ] Click stop button during playback
- [ ] Verify loading states work properly
- [ ] Test rapid clicking (should be prevented)
- [ ] Verify error feedback displays correctly

### FR-1.5: TTS Queue Management
- [ ] Start TTS in one tab, then start in another tab
- [ ] Verify first tab stops when second starts
- [ ] Test with multiple rapid requests
- [ ] Verify proper state synchronization across tabs
- [ ] Test with tab closing during playback

### FR-2.1 & FR-2.2: Settings Panel with Language Selection
- [ ] Open settings panel from Chrome toolbar
- [ ] Switch between Chinese and English
- [ ] Verify settings persist after browser restart
- [ ] Test settings sync across devices (if possible)
- [ ] Verify error handling for settings failures

### FR-2.3: Repeat Count Settings
- [ ] Adjust repeat count using slider
- [ ] Adjust repeat count using number input
- [ ] Verify synchronization between slider and input
- [ ] Test boundary values (1 and 10)
- [ ] Verify repeat count affects TTS playback

### FR-2.4: Floating Button Toggle Setting
- [ ] Toggle floating button on/off
- [ ] Verify button disappears when disabled
- [ ] Verify context menu still works when disabled
- [ ] Test settings persistence for toggle state

### FR-2.5: Settings Persistence
- [ ] Change settings and restart browser
- [ ] Verify settings are restored correctly
- [ ] Test with Chrome sync enabled
- [ ] Test settings validation and recovery

### FR-3: UI/UX Polish and Extension Icon
- [ ] Verify extension icon appears in Chrome toolbar
- [ ] Test popup UI responsiveness
- [ ] Verify accessibility features (keyboard navigation)
- [ ] Test with different screen sizes
- [ ] Verify visual consistency across all components

## Cross-Browser Compatibility
- [ ] Test on latest Chrome version
- [ ] Test on Chrome Beta (if available)
- [ ] Verify manifest v3 compatibility

## Performance Testing
- [ ] Test with large text selections (1000+ words)
- [ ] Monitor memory usage during extended use
- [ ] Test rapid text selections and TTS requests
- [ ] Verify no memory leaks after extended use

## Edge Cases and Error Handling
- [ ] Test with empty text selections
- [ ] Test with special characters and emojis
- [ ] Test on pages with no text content
- [ ] Test with network connectivity issues
- [ ] Test with corrupted settings
- [ ] Test extension disable/enable scenarios

## Website Compatibility
Test on various popular websites:
- [ ] Wikipedia
- [ ] News websites (CNN, BBC)
- [ ] Social media (Twitter, Facebook)
- [ ] E-commerce sites (Amazon)
- [ ] Educational sites
- [ ] Blogs and forums

## Accessibility Testing
- [ ] Test with screen readers
- [ ] Verify keyboard navigation works
- [ ] Test high contrast mode compatibility
- [ ] Verify focus indicators are visible
- [ ] Test with different font sizes

## Security Testing
- [ ] Verify no sensitive data is collected
- [ ] Test permissions are minimal and necessary
- [ ] Verify no external network requests
- [ ] Test content script isolation

## Installation and Uninstallation
- [ ] Test fresh installation
- [ ] Test extension updates
- [ ] Test uninstallation cleanup
- [ ] Verify no data persistence after uninstall

## User Experience Testing
- [ ] Test with non-technical users
- [ ] Verify intuitive operation
- [ ] Test error message clarity
- [ ] Verify help text is helpful
- [ ] Test overall user satisfaction

## Known Issues and Limitations
- TTS quality depends on system voices
- Some websites may block content scripts
- Performance may vary with very long texts
- Context menu may not appear on some protected pages

## Test Environment Setup
1. Install extension in Chrome
2. Enable Developer mode
3. Test on various websites
4. Monitor console for errors
5. Test with different user scenarios

## Reporting Issues
When reporting issues, include:
- Chrome version
- Operating system
- Website URL where issue occurred
- Steps to reproduce
- Expected vs actual behavior
- Console error messages (if any)
