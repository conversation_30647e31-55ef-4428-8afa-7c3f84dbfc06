// Gemini TTS Service for Read Aloud Pal Chrome Extension
// Handles text-to-speech using Google Gemini API

class GeminiTTSService {
  constructor() {
    this.apiEndpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent";
    this.currentAudio = null;
    this.isPlaying = false;
    this.audioContext = null;
    this.audioBuffer = null;
  }

  // Initialize audio context
  async initAudioContext() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    
    // Resume audio context if suspended (required by some browsers)
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  // Get the best voice for the given language
  getBestVoiceForLanguage(language) {
    const voiceMap = {
      'zh-TW': 'Kore',      // Firm voice for Traditional Chinese
      'zh-CN': 'Kore',      // Firm voice for Simplified Chinese  
      'en-US': 'Puck',      // Upbeat voice for English
      'en-GB': 'Charon',    // Informative voice for British English
      'ja-<PERSON>': 'Leda',      // Youthful voice for Japanese
      'ko-KR': 'Fenrir',    // Excitable voice for Korean
    };
    
    return voiceMap[language] || 'Kore'; // Default to Kore
  }

  // Generate speech using Gemini API
  async generateSpeech(text, language, apiKey) {
    try {
      if (!apiKey) {
        throw new Error('API key is required');
      }

      if (!text || text.trim().length === 0) {
        throw new Error('Text is required');
      }

      // Limit text length to avoid API limits
      if (text.length > 5000) {
        text = text.substring(0, 5000);
        console.warn('Text truncated to 5000 characters for TTS');
      }

      const voiceName = this.getBestVoiceForLanguage(language);
      
      const requestBody = {
        contents: [{
          parts: [{
            text: text
          }]
        }],
        generationConfig: {
          responseModalities: ["AUDIO"],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: {
                voiceName: voiceName
              }
            }
          }
        }
      };

      console.log(`🎯 Gemini TTS Request - Language: ${language}, Voice: ${voiceName}`);
      console.log(`Text: "${text.substring(0, 100)}..."`);

      const response = await fetch(`${this.apiEndpoint}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0] || !data.candidates[0].content.parts[0].inlineData) {
        throw new Error('Invalid response format from Gemini API');
      }

      const audioData = data.candidates[0].content.parts[0].inlineData.data;
      
      if (!audioData) {
        throw new Error('No audio data received from Gemini API');
      }

      console.log('✅ Gemini TTS response received successfully');
      return audioData;

    } catch (error) {
      console.error('Gemini TTS Error:', error);
      throw error;
    }
  }

  // Convert base64 audio data to audio buffer
  async base64ToAudioBuffer(base64Data) {
    try {
      // Decode base64 to array buffer
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      await this.initAudioContext();
      
      // Decode audio data
      const audioBuffer = await this.audioContext.decodeAudioData(bytes.buffer);
      return audioBuffer;
    } catch (error) {
      console.error('Error converting base64 to audio buffer:', error);
      throw new Error('Failed to process audio data');
    }
  }

  // Play audio buffer
  async playAudioBuffer(audioBuffer) {
    try {
      await this.initAudioContext();
      
      // Stop any currently playing audio
      this.stopAudio();
      
      // Create audio source
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);
      
      // Store reference for stopping
      this.currentAudio = source;
      this.isPlaying = true;
      
      return new Promise((resolve, reject) => {
        source.onended = () => {
          this.isPlaying = false;
          this.currentAudio = null;
          resolve();
        };
        
        source.onerror = (error) => {
          this.isPlaying = false;
          this.currentAudio = null;
          reject(error);
        };
        
        source.start(0);
      });
      
    } catch (error) {
      console.error('Error playing audio buffer:', error);
      this.isPlaying = false;
      this.currentAudio = null;
      throw error;
    }
  }

  // Stop current audio playback
  stopAudio() {
    if (this.currentAudio) {
      try {
        this.currentAudio.stop();
      } catch (error) {
        // Ignore errors when stopping
      }
      this.currentAudio = null;
    }
    this.isPlaying = false;
  }

  // Main method to speak text
  async speak(text, language, apiKey, onStart, onEnd, onError) {
    try {
      console.log(`🎤 Starting Gemini TTS for: "${text.substring(0, 50)}..."`);
      
      if (onStart) onStart();
      
      // Generate speech audio
      const audioData = await this.generateSpeech(text, language, apiKey);
      
      // Convert to audio buffer
      const audioBuffer = await this.base64ToAudioBuffer(audioData);
      
      // Play audio
      await this.playAudioBuffer(audioBuffer);
      
      console.log('✅ Gemini TTS playback completed');
      if (onEnd) onEnd();
      
    } catch (error) {
      console.error('Gemini TTS speak error:', error);
      if (onError) onError(error);
      throw error;
    }
  }

  // Check if currently playing
  getIsPlaying() {
    return this.isPlaying;
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GeminiTTSService;
} else if (typeof window !== 'undefined') {
  window.GeminiTTSService = GeminiTTSService;
}
