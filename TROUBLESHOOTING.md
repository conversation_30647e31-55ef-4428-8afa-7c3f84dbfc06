# Read Aloud Pal - 故障排除指南

## 問題：反白文字後點擊播放按鈕沒有聲音

### 快速診斷步驟

#### 1. 檢查瀏覽器控制台
1. 按 `F12` 打開開發者工具
2. 切換到 `Console` 標籤
3. 反白文字並點擊播放按鈕
4. 查看是否有錯誤訊息

**預期看到的訊息：**
```
TTS voices check: Found X voices for language zh-CN
TTS Request abc123: speaking "你好..." with options: {...}
Starting TTS for request abc123
TTS started for request abc123, repeat 1/1
```

#### 2. 執行TTS測試腳本
1. 在控制台中貼上並執行以下代碼：
```javascript
// 快速TTS測試
chrome.tts.speak("測試", {
  lang: "zh-CN",
  onEvent: (event) => console.log("TTS Event:", event.type, event)
});
```

#### 3. 檢查系統TTS設置

**Windows:**
- 設定 → 時間與語言 → 語音
- 確保已安裝中文語音包
- 測試語音功能

**macOS:**
- 系統偏好設定 → 輔助使用 → 語音
- 確保已安裝中文語音
- 測試語音功能

**Linux:**
- 安裝 espeak 或 festival
- 確保音頻系統正常運作

### 常見問題及解決方案

#### 問題1: 沒有TTS語音可用
**症狀：** 控制台顯示 "Found 0 voices"
**解決方案：**
1. 安裝系統TTS語音包
2. 重新啟動瀏覽器
3. 檢查瀏覽器TTS設置

#### 問題2: TTS超時
**症狀：** 控制台顯示 "TTS timeout"
**解決方案：**
1. 檢查系統音量設置
2. 確保沒有其他應用程式佔用音頻
3. 嘗試重新啟動瀏覽器

#### 問題3: 權限問題
**症狀：** 控制台顯示權限錯誤
**解決方案：**
1. 確保擴充功能已正確安裝
2. 檢查 manifest.json 中的 "tts" 權限
3. 重新載入擴充功能

#### 問題4: 網站相容性問題
**症狀：** 在某些網站上無法運作
**解決方案：**
1. 嘗試在簡單的網頁上測試（如 about:blank）
2. 檢查網站的內容安全政策
3. 確保網站允許擴充功能運行

### 詳細調試步驟

#### 步驟1: 檢查擴充功能狀態
```javascript
// 在控制台執行
console.log("Extension context:", chrome.runtime.id);
console.log("TTS API available:", !!chrome.tts);
```

#### 步驟2: 檢查語音列表
```javascript
// 在控制台執行
chrome.tts.getVoices((voices) => {
  console.log(`找到 ${voices.length} 個語音：`);
  voices.forEach((voice, i) => {
    console.log(`${i+1}. ${voice.voiceName} (${voice.lang}) - ${voice.remote ? '遠端' : '本地'}`);
  });
});
```

#### 步驟3: 測試基本TTS功能
```javascript
// 測試中文TTS
chrome.tts.speak("你好，這是測試", {
  lang: "zh-CN",
  rate: 1.0,
  onEvent: (event) => {
    console.log(`TTS事件: ${event.type}`, event);
    if (event.type === 'start') console.log("✅ TTS開始");
    if (event.type === 'end') console.log("✅ TTS結束");
    if (event.type === 'error') console.log("❌ TTS錯誤:", event.errorMessage);
  }
});
```

#### 步驟4: 檢查擴充功能設置
1. 點擊擴充功能圖標打開設置面板
2. 確認語言設置正確
3. 確認浮動按鈕已啟用
4. 嘗試調整重複次數

### 進階故障排除

#### 使用調試腳本
1. 載入 `debug-tts.js` 腳本
2. 在控制台執行完整診斷
3. 查看詳細的系統資訊

#### 檢查網路連線
某些TTS語音需要網路連線：
1. 確保網路連線正常
2. 檢查防火牆設置
3. 嘗試使用本地語音

#### 重置擴充功能
如果問題持續存在：
1. 停用擴充功能
2. 清除瀏覽器快取
3. 重新啟用擴充功能
4. 重新設置偏好

### 聯絡支援

如果以上步驟都無法解決問題，請提供以下資訊：

1. **瀏覽器資訊：**
   - Chrome版本
   - 作業系統版本

2. **錯誤訊息：**
   - 控制台完整錯誤訊息
   - TTS測試結果

3. **系統資訊：**
   - 可用的TTS語音列表
   - 音頻設備狀態

4. **重現步驟：**
   - 詳細的操作步驟
   - 測試的網站URL

### 常見解決方案摘要

✅ **最常見的解決方案：**
1. 安裝系統中文語音包
2. 重新啟動瀏覽器
3. 檢查系統音量設置
4. 確保沒有其他音頻應用程式衝突

✅ **快速測試：**
- 在 `about:blank` 頁面測試
- 使用右鍵選單而非浮動按鈕
- 嘗試英文文字測試

這個故障排除指南應該能幫助您解決大部分TTS相關的問題。
