// Content script for Read Aloud Pal Chrome Extension
// Handles text selection detection and floating button display

class ReadAloudPal {
  constructor() {
    this.floatingButton = null;
    this.selectedText = "";
    this.isPlaying = false;
    this.isLoading = false;
    this.showFloatingButton = true;
    this.currentSelection = null;
    this.currentRequestId = null;
    this.audioContext = null;
    this.currentAudioSource = null;
    this.currentRepeatCount = 0;
    this.totalRepeats = 1;
    this.currentAudioData = null;
    this.loadingOverlay = null;
    this.requestTimeoutId = null;
    this.processingSteps = [
      "正在發送文字到 Gemini API...",
      "Gemini 正在生成語音...",
      "正在處理音頻數據...",
      "準備播放...",
    ];
    this.currentStep = 0;
    this.highlightedElements = new Set();
    this.highlightClass = "read-aloud-pal-highlighted";

    this.init();
  }

  async init() {
    try {
      // Get user settings with error handling
      const settings = await chrome.storage.sync
        .get(["showFloatingButton"])
        .catch(() => ({}));
      this.showFloatingButton = settings.showFloatingButton !== false;

      // Add event listeners with error handling
      document.addEventListener("mouseup", this.handleTextSelection.bind(this));
      document.addEventListener("mousedown", this.handleMouseDown.bind(this));

      // Add error handler for unhandled errors
      window.addEventListener("error", this.handleGlobalError.bind(this));

      // Listen for messages from background script
      chrome.runtime.onMessage.addListener(
        this.handleBackgroundMessage.bind(this)
      );

      console.log("Read Aloud Pal content script initialized");
    } catch (error) {
      console.error("Error initializing Read Aloud Pal:", error);
    }
  }

  // Handle global errors
  handleGlobalError(event) {
    if (
      event.error &&
      event.error.message &&
      event.error.message.includes("Read Aloud Pal")
    ) {
      console.error("Read Aloud Pal error:", event.error);
      // Optionally show user-friendly error
      this.showTemporaryFeedback("Extension error occurred");
    }
  }

  handleMouseDown(event) {
    // Don't hide the floating button if the user is clicking on it
    if (this.floatingButton && event.target === this.floatingButton) {
      return;
    }
    // Hide floating button on any other mousedown
    this.hideFloatingButton();
  }

  handleTextSelection(event) {
    // Small delay to ensure selection is complete
    setTimeout(() => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();

      // Only show button for meaningful text selections (at least 1 character, max 5000 characters)
      if (
        selectedText &&
        selectedText.length >= 1 &&
        selectedText.length <= 5000 &&
        this.showFloatingButton
      ) {
        this.selectedText = selectedText;
        this.displayFloatingButton(selection);
      } else {
        this.hideFloatingButton();
      }
    }, 10);
  }

  displayFloatingButton(selection) {
    // Remove existing button
    this.hideFloatingButton();

    // Validate selection
    if (!selection.rangeCount) return;

    // Get selection position
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // Skip if selection is not visible
    if (rect.width === 0 && rect.height === 0) return;

    // Create floating button
    this.floatingButton = document.createElement("div");
    this.floatingButton.className = "read-aloud-pal-button";
    this.floatingButton.innerHTML = this.isPlaying ? "⏹️" : "▶️";
    this.floatingButton.title = this.isPlaying ? "Stop reading" : "Read aloud";

    // Calculate optimal position
    let left = rect.right + 5;
    let top = rect.top - 5;

    // Ensure button stays within viewport
    const buttonSize = 24;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (left + buttonSize > viewportWidth) {
      left = rect.left - buttonSize - 5; // Position to the left of selection
    }

    if (top < 0) {
      top = rect.bottom + 5; // Position below selection
    } else if (top + buttonSize > viewportHeight) {
      top = rect.top - buttonSize - 5; // Position above selection
    }

    // Position the button
    this.floatingButton.style.position = "fixed";
    this.floatingButton.style.left = `${left}px`;
    this.floatingButton.style.top = `${top}px`;
    this.floatingButton.style.zIndex = "2147483647"; // Maximum z-index

    // Add click event
    this.floatingButton.addEventListener(
      "click",
      this.handleButtonClick.bind(this)
    );

    // Add to page
    document.body.appendChild(this.floatingButton);
  }

  hideFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.remove();
      this.floatingButton = null;
    }
  }

  handleButtonClick(event) {
    console.log("🔘 Floating button clicked!", {
      selectedText: this.selectedText,
      isPlaying: this.isPlaying,
      isLoading: this.isLoading,
    });

    event.preventDefault();
    event.stopPropagation();

    // Prevent multiple clicks during loading
    if (this.isLoading) {
      console.log("⏳ Button click ignored - already loading");
      return;
    }

    if (this.isPlaying) {
      // Stop current speech (both Chrome TTS and Gemini audio)
      console.log("⏹️ Stopping current speech");
      this.setLoadingState(true);

      // Stop local audio playback
      this.stopCurrentAudio();

      // Hide processing overlay
      this.hideProcessingOverlay();

      // Clear highlights
      this.clearHighlights();

      // Also notify background script to stop
      chrome.runtime.sendMessage({ action: "stop" }, (response) => {
        console.log("Stop response:", response);
        if (chrome.runtime.lastError) {
          console.error("Error stopping speech:", chrome.runtime.lastError);
        }
        this.setLoadingState(false);
        this.isPlaying = false;
        this.currentRequestId = null;
        this.currentAudioData = null;
        this.updateButtonState();
      });
    } else {
      // Start speech
      if (this.selectedText && this.selectedText.trim().length > 0) {
        console.log(
          "▶️ Starting speech with text:",
          this.selectedText.substring(0, 50) + "..."
        );
        this.setLoadingState(true);
        console.log("🔄 Showing processing overlay...");
        this.showProcessingOverlay();
        console.log("✨ Highlighting selected text...");
        this.highlightSelectedText();

        const localRequestId = `local-${Date.now()}`;
        this.currentRequestId = localRequestId;

        // Set a timeout to handle cases where the background script doesn't respond
        this.requestTimeoutId = setTimeout(() => {
          if (this.isLoading && this.currentRequestId === localRequestId) {
            console.error(
              "Background script did not respond in time. Resetting state."
            );
            this.setLoadingState(false);
            this.hideProcessingOverlay();
            this.clearHighlights();
            this.showTemporaryFeedback(
              "Error: No response from background script"
            );
            this.currentRequestId = null;
          }
        }, 10000); // 10-second timeout

        chrome.runtime.sendMessage(
          {
            action: "speak",
            text: this.selectedText,
          },
          (response) => {
            console.log("Speak response:", response);
            if (chrome.runtime.lastError) {
              console.error("Error starting speech:", chrome.runtime.lastError);
              this.setLoadingState(false);
              this.hideProcessingOverlay();
              this.clearHighlights(); // Also clear highlights on send error
              this.showTemporaryFeedback(
                "Error: " + chrome.runtime.lastError.message
              );
              if (this.requestTimeoutId) clearTimeout(this.requestTimeoutId);
            } else if (response && response.requestId) {
              // The background script has received our request and sent back its own ID.
              // We can now clear our local timeout.
              if (this.requestTimeoutId) clearTimeout(this.requestTimeoutId);
              this.currentRequestId = response.requestId;
              // Update processing step
              this.updateProcessingStep(1);
            }
          }
        );
      } else {
        // No text selected, show feedback
        console.log("❌ No text selected for speech");
        this.showTemporaryFeedback("No text selected");
      }
    }
  }

  // Set loading state with visual feedback
  setLoadingState(loading) {
    this.isLoading = loading;
    this.updateButtonState();
  }

  // Show temporary feedback to user
  showTemporaryFeedback(message) {
    if (this.floatingButton) {
      const originalContent = this.floatingButton.innerHTML;
      const originalTitle = this.floatingButton.title;

      this.floatingButton.innerHTML = "⚠️";
      this.floatingButton.title = message;
      this.floatingButton.style.backgroundColor = "rgba(255, 193, 7, 0.9)";

      setTimeout(() => {
        if (this.floatingButton) {
          this.floatingButton.innerHTML = originalContent;
          this.floatingButton.title = originalTitle;
          this.floatingButton.style.backgroundColor = "";
        }
      }, 1500);
    }
  }

  handleBackgroundMessage(message, sender, sendResponse) {
    // If we receive any message from the background script, it means it's alive.
    // Clear the request timeout.
    if (this.requestTimeoutId) {
      clearTimeout(this.requestTimeoutId);
      this.requestTimeoutId = null;
    }

    switch (message.action) {
      case "speechStart":
        // Only update if this is for our current request or we don't have one
        if (
          !this.currentRequestId ||
          message.requestId === this.currentRequestId
        ) {
          this.isPlaying = true;
          this.isLoading = false;
          this.currentRequestId = message.requestId;
          this.updateButtonState();
        }
        break;

      case "speechComplete":
        // Only update if this is for our current request
        if (message.requestId === this.currentRequestId) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.updateButtonState();

          // Remove speaking animation immediately
          this.removeSpeakingAnimationFromHighlights();

          // Clear highlights after a delay to let user see what was read
          this.clearHighlightsAfterDelay(2000);

          if (message.reason === "interrupted") {
            this.showTemporaryFeedback("Speech interrupted by new request");
          }
        }
        break;

      case "speechError":
        // Only update if this is for our current request
        if (message.requestId === this.currentRequestId) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.hideProcessingOverlay();
          this.clearHighlights();
          this.updateButtonState();
          console.error("Speech error:", message.error);
          this.showTemporaryFeedback(
            "Speech error: " + (message.error || "Unknown error")
          );
        }
        break;

      case "speechStoppedGlobally":
        // Another tab started speech, update our state
        if (this.isPlaying) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.updateButtonState();
          // Clear highlights after a delay when interrupted by another tab
          this.clearHighlightsAfterDelay(1000);
          this.showTemporaryFeedback("Speech started in another tab");
        }
        break;

      case "playGeminiAudio":
        // Handle Gemini TTS audio playback
        this.handleGeminiAudioPlayback(message);
        break;

      case "updateProcessingStep":
        // Update processing step
        this.updateProcessingStep(message.step);
        break;

      case "settingsChanged":
        // Update settings when changed from popup
        this.showFloatingButton = message.settings.showFloatingButton !== false;
        if (!this.showFloatingButton) {
          this.hideFloatingButton();
        }
        break;
    }
  }

  updateButtonState() {
    if (this.floatingButton) {
      // Remove all state attributes
      this.floatingButton.removeAttribute("data-loading");
      this.floatingButton.removeAttribute("data-playing");

      if (this.isLoading) {
        this.floatingButton.innerHTML = "⏳";
        this.floatingButton.title = "Loading...";
        this.floatingButton.setAttribute("data-loading", "true");
        this.floatingButton.style.backgroundColor = "rgba(108, 117, 125, 0.9)"; // Gray for loading
      } else if (this.isPlaying) {
        this.floatingButton.innerHTML = "⏹️";
        this.floatingButton.title = "Stop reading (Click to stop)";
        this.floatingButton.setAttribute("data-playing", "true");
        this.floatingButton.style.backgroundColor = "rgba(220, 53, 69, 0.9)"; // Red for stop
      } else {
        this.floatingButton.innerHTML = "▶️";
        this.floatingButton.title = "Read aloud (Click to play)";
        this.floatingButton.style.backgroundColor = "rgba(74, 144, 226, 0.9)"; // Blue for play
      }
    }
  }

  // Initialize audio context
  async initAudioContext() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
    }

    // Resume audio context if suspended (required by some browsers)
    if (this.audioContext.state === "suspended") {
      await this.audioContext.resume();
    }
  }

  // Handle Gemini audio playback
  async handleGeminiAudioPlayback(message) {
    try {
      console.log(
        `🎵 Handling Gemini audio playback for request ${message.requestId}`
      );

      // Update processing step
      this.updateProcessingStep(2);

      // Update state
      this.currentRequestId = message.requestId;
      this.currentAudioData = message.audioData;
      this.totalRepeats = message.repeatCount || 1;
      this.currentRepeatCount = 0;
      this.isLoading = false;
      this.isPlaying = true;
      this.updateButtonState();

      // Update to final step
      this.updateProcessingStep(3);

      // Show final step briefly before starting playback
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Add speaking animation to highlights
      this.addSpeakingAnimationToHighlights();

      // Start playback with repeat functionality
      await this.playGeminiAudioWithRepeat();

      // Hide overlay when playback starts
      this.hideProcessingOverlay();
    } catch (error) {
      console.error("Error handling Gemini audio playback:", error);
      this.handleAudioError(error.message);
    }
  }

  // Play Gemini audio with repeat functionality
  async playGeminiAudioWithRepeat() {
    try {
      if (this.currentRepeatCount >= this.totalRepeats) {
        // Playback complete
        this.handleAudioComplete();
        return;
      }

      console.log(
        `🔊 Playing audio, repeat ${this.currentRepeatCount + 1}/${
          this.totalRepeats
        }`
      );

      // Convert base64 to audio buffer
      const audioBuffer = await this.base64ToAudioBuffer(this.currentAudioData);

      // Play audio buffer
      await this.playAudioBuffer(audioBuffer);

      // Increment repeat count
      this.currentRepeatCount++;

      // Continue with next repeat after a short pause
      if (this.currentRepeatCount < this.totalRepeats && this.isPlaying) {
        setTimeout(() => {
          if (this.isPlaying) {
            this.playGeminiAudioWithRepeat();
          }
        }, 300); // 300ms pause between repeats
      } else {
        this.handleAudioComplete();
      }
    } catch (error) {
      console.error("Error playing Gemini audio:", error);
      this.handleAudioError(error.message);
    }
  }

  // Convert base64 audio data to audio buffer
  async base64ToAudioBuffer(base64Data) {
    try {
      // Decode base64 to array buffer
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      await this.initAudioContext();

      try {
        // Try to decode as standard audio format first
        const audioBuffer = await this.audioContext.decodeAudioData(
          bytes.buffer.slice()
        );
        return audioBuffer;
      } catch (decodeError) {
        console.log("Standard audio decode failed, trying PCM format...");
        // If standard decode fails, treat as raw PCM data
        return this.createPCMAudioBuffer(bytes);
      }
    } catch (error) {
      console.error("Error converting base64 to audio buffer:", error);
      throw new Error("Failed to process audio data: " + error.message);
    }
  }

  // Create audio buffer from raw PCM data
  createPCMAudioBuffer(pcmData) {
    try {
      // Gemini TTS returns 16-bit PCM at 24kHz, mono
      const sampleRate = 24000;
      const channels = 1;
      const bytesPerSample = 2; // 16-bit = 2 bytes

      const numSamples = pcmData.length / bytesPerSample;
      const audioBuffer = this.audioContext.createBuffer(
        channels,
        numSamples,
        sampleRate
      );

      const channelData = audioBuffer.getChannelData(0);

      // Convert 16-bit PCM to float32 (-1.0 to 1.0)
      for (let i = 0; i < numSamples; i++) {
        const byteIndex = i * bytesPerSample;
        // Read 16-bit little-endian signed integer
        const sample = (pcmData[byteIndex + 1] << 8) | pcmData[byteIndex];
        // Convert to signed 16-bit
        const signedSample = sample > 32767 ? sample - 65536 : sample;
        // Normalize to -1.0 to 1.0
        channelData[i] = signedSample / 32768.0;
      }

      console.log(
        `✅ Created PCM audio buffer: ${numSamples} samples, ${sampleRate}Hz`
      );
      return audioBuffer;
    } catch (error) {
      console.error("Error creating PCM audio buffer:", error);
      throw new Error("Failed to create PCM audio buffer");
    }
  }

  // Play audio buffer
  async playAudioBuffer(audioBuffer) {
    try {
      await this.initAudioContext();

      // Stop any currently playing audio
      this.stopCurrentAudio();

      // Create audio source
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);

      // Store reference for stopping
      this.currentAudioSource = source;

      return new Promise((resolve, reject) => {
        source.onended = () => {
          this.currentAudioSource = null;
          resolve();
        };

        source.onerror = (error) => {
          this.currentAudioSource = null;
          reject(error);
        };

        source.start(0);
      });
    } catch (error) {
      console.error("Error playing audio buffer:", error);
      this.currentAudioSource = null;
      throw error;
    }
  }

  // Stop current audio playback
  stopCurrentAudio() {
    if (this.currentAudioSource) {
      try {
        this.currentAudioSource.stop();
      } catch (error) {
        // Ignore errors when stopping
      }
      this.currentAudioSource = null;
    }
  }

  // Handle audio playback completion
  handleAudioComplete() {
    console.log("✅ Gemini audio playback completed");
    this.isPlaying = false;
    this.isLoading = false;
    this.currentRequestId = null;
    this.currentAudioData = null;
    this.currentRepeatCount = 0;
    this.totalRepeats = 1;
    this.updateButtonState();

    // Remove speaking animation
    this.removeSpeakingAnimationFromHighlights();

    // Clear highlights after a delay
    this.clearHighlightsAfterDelay(3000);

    // Notify background script
    chrome.runtime
      .sendMessage({
        action: "audioPlaybackComplete",
      })
      .catch(() => {});
  }

  // Handle audio playback error
  handleAudioError(errorMessage) {
    console.error("Gemini audio error:", errorMessage);
    this.isPlaying = false;
    this.isLoading = false;
    this.currentRequestId = null;
    this.currentAudioData = null;
    this.currentRepeatCount = 0;
    this.totalRepeats = 1;
    this.hideProcessingOverlay();
    this.clearHighlights();
    this.updateButtonState();
    this.showTemporaryFeedback("Audio error: " + errorMessage);

    // Notify background script
    chrome.runtime
      .sendMessage({
        action: "audioPlaybackError",
        error: errorMessage,
      })
      .catch(() => {});
  }

  // Show processing overlay
  showProcessingOverlay() {
    console.log("📱 showProcessingOverlay called");

    if (this.loadingOverlay) {
      console.log("🗑️ Removing existing overlay");
      this.hideProcessingOverlay();
    }

    this.currentStep = 0;
    this.loadingOverlay = document.createElement("div");
    this.loadingOverlay.className = "read-aloud-pal-loading-overlay";

    // Add inline styles as fallback
    this.loadingOverlay.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      background: rgba(0, 0, 0, 0.7) !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      z-index: 2147483647 !important;
      backdrop-filter: blur(4px) !important;
    `;

    this.loadingOverlay.innerHTML = `
      <div class="loading-content" style="
        background: white !important;
        border-radius: 16px !important;
        padding: 32px !important;
        text-align: center !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        max-width: 320px !important;
        width: 90% !important;
      ">
        <div class="loading-spinner" style="
          width: 48px !important;
          height: 48px !important;
          border: 4px solid #e9ecef !important;
          border-top: 4px solid #4a90e2 !important;
          border-radius: 50% !important;
          animation: spin 1s linear infinite !important;
          margin: 0 auto 16px !important;
        "></div>
        <div class="loading-text" style="
          font-size: 16px !important;
          font-weight: 500 !important;
          color: #495057 !important;
          margin-bottom: 16px !important;
          line-height: 1.4 !important;
        ">${this.processingSteps[0]}</div>
        <div class="loading-progress" style="margin-top: 16px !important;">
          <div class="progress-bar" style="
            width: 100% !important;
            height: 6px !important;
            background: #e9ecef !important;
            border-radius: 3px !important;
            overflow: hidden !important;
            margin-bottom: 8px !important;
          ">
            <div class="progress-fill" style="
              height: 100% !important;
              background: linear-gradient(90deg, #4a90e2, #357abd) !important;
              border-radius: 3px !important;
              transition: width 0.3s ease !important;
              width: 25% !important;
            "></div>
          </div>
          <div class="progress-text" style="
            font-size: 12px !important;
            color: #6c757d !important;
            font-weight: 500 !important;
          ">步驟 1 / 4</div>
        </div>
      </div>
    `;

    document.body.appendChild(this.loadingOverlay);
    console.log("✅ Loading overlay added to DOM");

    // Add click handler to hide overlay
    this.loadingOverlay.addEventListener("click", (e) => {
      if (e.target === this.loadingOverlay) {
        this.hideProcessingOverlay();
      }
    });
  }

  // Update processing step
  updateProcessingStep(step) {
    if (!this.loadingOverlay || step >= this.processingSteps.length) return;

    this.currentStep = step;
    const loadingText = this.loadingOverlay.querySelector(".loading-text");
    const progressFill = this.loadingOverlay.querySelector(".progress-fill");
    const progressText = this.loadingOverlay.querySelector(".progress-text");

    if (loadingText) {
      loadingText.textContent = this.processingSteps[step];
    }

    if (progressFill) {
      const progress = ((step + 1) / this.processingSteps.length) * 100;
      progressFill.style.width = `${progress}%`;
    }

    if (progressText) {
      progressText.textContent = `步驟 ${step + 1} / ${
        this.processingSteps.length
      }`;
    }
  }

  // Hide processing overlay
  hideProcessingOverlay() {
    if (this.loadingOverlay) {
      this.loadingOverlay.remove();
      this.loadingOverlay = null;
    }
    this.currentStep = 0;
  }

  // Highlight selected text
  highlightSelectedText() {
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    try {
      const range = selection.getRangeAt(0);
      const selectedText = selection.toString().trim();

      if (!selectedText) return;

      // Simple highlight approach - wrap the selection in a span
      this.simpleHighlight(range);

      console.log(`✨ Highlighted text: "${selectedText.substring(0, 50)}..."`);
    } catch (error) {
      console.error("Error highlighting text:", error);
    }
  }

  // Simple highlight method
  simpleHighlight(range) {
    try {
      console.log("🎨 Starting simple highlight");

      // Check if range is valid
      if (range.collapsed) {
        console.log("❌ Range is collapsed, skipping highlight");
        return;
      }

      // Create highlight element
      const highlight = document.createElement("span");
      highlight.className = this.highlightClass;
      highlight.setAttribute("data-read-aloud-pal", "highlighted");

      // Add inline styles as fallback with animation
      highlight.style.cssText = `
        background: linear-gradient(120deg, #a8e6cf 0%, #88d8a3 100%) !important;
        color: #2d5a3d !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        position: relative !important;
        font-weight: 500 !important;
        animation: highlightFadeIn 0.3s ease-out !important;
        transition: all 0.2s ease !important;
      `;

      // Surround the range contents with highlight
      try {
        range.surroundContents(highlight);
        this.highlightedElements.add(highlight);
        console.log("✨ Text highlighted successfully with surroundContents");
      } catch (e) {
        console.log(
          "⚠️ surroundContents failed, trying extraction method:",
          e.message
        );
        // If surroundContents fails, try extracting and wrapping
        try {
          const contents = range.extractContents();
          highlight.appendChild(contents);
          range.insertNode(highlight);
          this.highlightedElements.add(highlight);
          console.log("✨ Text highlighted with extraction method");
        } catch (e2) {
          console.error("❌ Both highlight methods failed:", e2);
          // Last resort: clone contents and insert
          try {
            const contents = range.cloneContents();
            highlight.appendChild(contents);
            range.deleteContents();
            range.insertNode(highlight);
            this.highlightedElements.add(highlight);
            console.log("✨ Text highlighted with clone method");
          } catch (e3) {
            console.error("❌ All highlight methods failed:", e3);
          }
        }
      }
    } catch (error) {
      console.error("Error in simple highlight:", error);
    }
  }

  // Clear all highlights
  clearHighlights() {
    this.highlightedElements.forEach((element) => {
      try {
        if (element.parentNode) {
          const textNode = document.createTextNode(element.textContent);
          element.parentNode.replaceChild(textNode, element);
        }
      } catch (error) {
        console.error("Error removing highlight:", error);
      }
    });
    this.highlightedElements.clear();
    console.log("🧹 Cleared all text highlights");
  }

  // Clear highlights after a delay
  clearHighlightsAfterDelay(delay = 5000) {
    setTimeout(() => {
      this.clearHighlights();
    }, delay);
  }

  // Add speaking animation to highlighted elements
  addSpeakingAnimationToHighlights() {
    this.highlightedElements.forEach((element) => {
      if (element.parentNode) {
        element.classList.add("speaking");
      }
    });
    console.log("🎭 Added speaking animation to highlights");
  }

  // Remove speaking animation from highlighted elements
  removeSpeakingAnimationFromHighlights() {
    this.highlightedElements.forEach((element) => {
      if (element.parentNode) {
        element.classList.remove("speaking");
      }
    });
    console.log("🎭 Removed speaking animation from highlights");
  }
}

// Initialize the extension with safety check
if (!window.readAloudPalInstance) {
  window.readAloudPalInstance = new ReadAloudPal();
  console.log("Read Aloud Pal content script initialized");
} else {
  console.log("Read Aloud Pal content script already initialized");
}
