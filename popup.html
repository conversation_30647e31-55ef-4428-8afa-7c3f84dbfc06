<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Read Aloud Pal Settings</title>
    <link rel="stylesheet" href="popup.css" />
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img src="icons/icon32.png" alt="Read Aloud Pal" class="logo" />
        <h1>朗讀小夥伴</h1>
        <p class="subtitle">Read Aloud Pal</p>
      </div>

      <div class="settings">
        <div class="setting-group">
          <label for="api-key-input">Google Gemini API Key:</label>
          <div class="api-key-control">
            <input
              type="password"
              id="api-key-input"
              placeholder="Enter your Gemini API key..."
              class="setting-input api-key-input"
            />
            <button
              id="toggle-api-key"
              class="toggle-visibility-btn"
              type="button"
            >
              👁️
            </button>
          </div>
          <div class="api-key-description">
            <span id="api-key-status">No API key configured</span>
            <a
              href="https://aistudio.google.com/app/apikey"
              target="_blank"
              class="api-key-link"
            >
              Get your API key from Google AI Studio
            </a>
          </div>
        </div>

        <div class="setting-group">
          <label for="language-select">語言 / Language:</label>
          <select id="language-select" class="setting-input">
            <option value="zh-TW">中文 (繁體) - Traditional Chinese</option>
            <option value="zh-CN">中文 (簡體) - Simplified Chinese</option>
            <option value="en-US">English (US)</option>
            <option value="en-GB">English (UK)</option>
            <option value="ja-JP">日本語 - Japanese</option>
            <option value="ko-KR">한국어 - Korean</option>
          </select>
          <div class="language-description">
            <span id="language-status">Current: Traditional Chinese</span>
          </div>
        </div>

        <div class="setting-group">
          <label for="voice-select">語音風格 / Voice Style:</label>
          <select id="voice-select" class="setting-input">
            <option value="Kore">Kore - Firm (堅定)</option>
            <option value="Puck">Puck - Upbeat (樂觀)</option>
            <option value="Charon">Charon - Informative (資訊性)</option>
            <option value="Leda">Leda - Youthful (年輕)</option>
            <option value="Fenrir">Fenrir - Excitable (興奮)</option>
            <option value="Aoede">Aoede - Breezy (輕快)</option>
            <option value="Zephyr">Zephyr - Bright (明亮)</option>
            <option value="Orus">Orus - Firm (堅定)</option>
            <option value="Callirrhoe">Callirrhoe - Easy-going (隨和)</option>
            <option value="Autonoe">Autonoe - Bright (明亮)</option>
            <option value="Enceladus">Enceladus - Breathy (氣息感)</option>
            <option value="Iapetus">Iapetus - Clear (清晰)</option>
            <option value="Umbriel">Umbriel - Easy-going (隨和)</option>
            <option value="Algieba">Algieba - Smooth (順滑)</option>
            <option value="Despina">Despina - Smooth (順滑)</option>
          </select>
          <div class="voice-description">
            <span id="voice-status">Current: Kore - Firm</span>
          </div>
        </div>

        <div class="setting-group">
          <label for="repeat-count"
            >重複次數 / Repeat Count: <span id="repeat-display">1</span></label
          >
          <div class="repeat-control">
            <input
              type="range"
              id="repeat-slider"
              min="1"
              max="10"
              value="1"
              class="slider"
            />
            <input
              type="number"
              id="repeat-input"
              min="1"
              max="10"
              value="1"
              class="number-input"
            />
          </div>
          <div class="repeat-description">
            <span id="repeat-text">Text will be read once</span>
          </div>
        </div>

        <div class="setting-group">
          <label class="toggle-label">
            <input type="checkbox" id="floating-button-toggle" checked />
            <span class="toggle-slider"></span>
            <span class="toggle-text">顯示浮動按鈕 / Show Floating Button</span>
          </label>
          <div class="toggle-description">
            <span id="toggle-status"
              >Floating button will appear when text is selected</span
            >
          </div>
        </div>
      </div>

      <div class="history-section">
        <div class="history-header">
          <h3>🎵 語音歷史紀錄</h3>
          <button
            id="clear-history-btn"
            class="clear-history-btn"
            title="清除所有歷史紀錄"
          >
            🗑️
          </button>
        </div>
        <div id="history-list" class="history-list">
          <div class="history-empty">
            <span>尚無語音紀錄</span>
            <small>選擇文字並使用TTS功能後，歷史紀錄會顯示在這裡</small>
          </div>
        </div>
      </div>

      <div class="footer">
        <p class="version">Version 1.0.0</p>
        <p class="description">
          Select text on any webpage and click the floating button or
          right-click to read aloud!
        </p>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
