<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Floating <PERSON>ton - Read Aloud Pal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 5px;
        }
        .test-text {
            font-size: 16px;
            line-height: 1.8;
            margin: 10px 0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Floating Button - Read Aloud Pal</h1>
        
        <div class="instructions">
            <h3>📋 Debug Instructions</h3>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Go to the Console tab</li>
                <li>Select any text below</li>
                <li>Look for the floating button</li>
                <li>Click the floating button</li>
                <li>Check console for debug messages</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🇺🇸 English Test Text</h3>
            <p class="test-text">
                This is a test paragraph for debugging the floating button functionality. 
                Select this text and check if the floating button appears and responds to clicks. 
                The button should trigger console log messages when clicked.
            </p>
        </div>

        <div class="test-section">
            <h3>🇨🇳 Chinese Test Text</h3>
            <p class="test-text">
                這是用於調試浮動按鈕功能的測試段落。選擇此文字並檢查浮動按鈕是否出現並響應點擊。
                按鈕被點擊時應該觸發控制台日誌消息。
            </p>
        </div>

        <div class="test-section">
            <h3>🔤 Mixed Language Test</h3>
            <p class="test-text">
                This paragraph contains both English and Chinese text: 這是混合語言測試。
                Select different parts to test the floating button behavior with various text selections.
                測試不同的文字選擇以檢查浮動按鈕的行為。
            </p>
        </div>

        <div class="debug-info">
            <h4>🐛 Debug Information</h4>
            <p>Expected console messages when floating button is clicked:</p>
            <ul>
                <li><code>🔘 Floating button clicked!</code> - Button click detected</li>
                <li><code>▶️ Starting speech with text:</code> - Speech process initiated</li>
                <li><code>🔄 Showing processing overlay...</code> - Loading screen shown</li>
                <li><code>✨ Highlighting selected text...</code> - Text highlighting applied</li>
            </ul>
            <p>If these messages don't appear, the floating button is not working properly.</p>
        </div>

        <div class="test-section">
            <h3>🎯 Long Text Test</h3>
            <p class="test-text">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                
                Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, 
                totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
            </p>
        </div>
    </div>

    <script>
        // Add some debug logging to help identify issues
        console.log('🔧 Debug page loaded');
        
        // Monitor text selection
        document.addEventListener('mouseup', function() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            if (selectedText) {
                console.log('📝 Text selected:', selectedText.substring(0, 50) + '...');
                console.log('📍 Selection range count:', selection.rangeCount);
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const rect = range.getBoundingClientRect();
                    console.log('📐 Selection rect:', {
                        width: rect.width,
                        height: rect.height,
                        top: rect.top,
                        left: rect.left
                    });
                }
            }
        });

        // Monitor for floating button creation
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.className && node.className.includes('read-aloud-pal-button')) {
                        console.log('🎯 Floating button detected in DOM:', node);
                        console.log('🎯 Button styles:', {
                            position: node.style.position,
                            left: node.style.left,
                            top: node.style.top,
                            zIndex: node.style.zIndex,
                            pointerEvents: getComputedStyle(node).pointerEvents
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
